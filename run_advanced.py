#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النسخة المتقدمة من شاشة أوقات الصلاة
Run Advanced Version of Mosque Prayer Times Display

هذا الملف يقوم بتشغيل النسخة المتقدمة مع جميع الميزات المطورة
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import subprocess
import importlib.util

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ إصدار Python مناسب: {sys.version}")
    return True

def check_required_modules():
    """التحقق من المكتبات المطلوبة"""
    required_basic = [
        'tkinter',
        'requests', 
        'pygame',
        'PIL',  # Pillow
        'hijri_converter',
        'pyttsx3'
    ]
    
    advanced_modules = [
        'matplotlib',
        'pandas', 
        'numpy',
        'cv2',  # opencv-python
        'flask',
        'sqlalchemy',
        'sklearn',  # scikit-learn
        'bcrypt',
        'psutil'
    ]
    
    missing_basic = []
    missing_advanced = []
    
    # فحص المكتبات الأساسية
    for module in required_basic:
        try:
            if module == 'PIL':
                import PIL
            else:
                __import__(module)
            print(f"✅ {module}")
        except ImportError:
            missing_basic.append(module)
            print(f"❌ {module}")
    
    # فحص المكتبات المتقدمة
    for module in advanced_modules:
        try:
            if module == 'cv2':
                import cv2
            elif module == 'sklearn':
                import sklearn
            else:
                __import__(module)
            print(f"✅ {module} (متقدم)")
        except ImportError:
            missing_advanced.append(module)
            print(f"⚠️ {module} (متقدم - اختياري)")
    
    return missing_basic, missing_advanced

def install_missing_modules(missing_modules, advanced=False):
    """تثبيت المكتبات المفقودة"""
    if not missing_modules:
        return True
    
    module_type = "المتقدمة" if advanced else "الأساسية"
    print(f"\n🔧 تثبيت المكتبات {module_type} المفقودة...")
    
    # تحويل أسماء المكتبات للتثبيت
    install_names = {
        'PIL': 'Pillow',
        'cv2': 'opencv-python',
        'sklearn': 'scikit-learn'
    }
    
    for module in missing_modules:
        install_name = install_names.get(module, module)
        
        try:
            print(f"📦 تثبيت {install_name}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', install_name
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت {install_name}")
            else:
                print(f"❌ فشل تثبيت {install_name}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تثبيت {install_name}: {e}")
            return False
    
    return True

def show_startup_options():
    """عرض خيارات التشغيل"""
    print("\n" + "="*60)
    print("🕌 شاشة أوقات الصلاة للمسجد - النسخة المتقدمة")
    print("="*60)
    print("\nخيارات التشغيل:")
    print("1. تشغيل النسخة المتقدمة (جميع الميزات)")
    print("2. تشغيل النسخة الأساسية (الميزات الأساسية فقط)")
    print("3. تثبيت المتطلبات المتقدمة")
    print("4. فحص النظام")
    print("5. خروج")
    
    while True:
        try:
            choice = input("\nاختر رقم الخيار (1-5): ").strip()
            
            if choice in ['1', '2', '3', '4', '5']:
                return int(choice)
            else:
                print("⚠️ يرجى اختيار رقم صحيح من 1 إلى 5")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم إلغاء التشغيل")
            return 5
        except Exception:
            print("⚠️ خطأ في الإدخال، يرجى المحاولة مرة أخرى")

def run_advanced_version():
    """تشغيل النسخة المتقدمة"""
    try:
        print("\n🚀 تشغيل النسخة المتقدمة...")
        
        # استيراد وتشغيل التطبيق المتقدم
        from advanced_main import main
        main()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد النسخة المتقدمة: {e}")
        print("💡 جرب تشغيل النسخة الأساسية أو تثبيت المتطلبات")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل النسخة المتقدمة: {e}")
        return False
    
    return True

def run_basic_version():
    """تشغيل النسخة الأساسية"""
    try:
        print("\n🚀 تشغيل النسخة الأساسية...")
        
        # استيراد وتشغيل التطبيق الأساسي
        from main import main
        main()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد النسخة الأساسية: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل النسخة الأساسية: {e}")
        return False
    
    return True

def install_advanced_requirements():
    """تثبيت المتطلبات المتقدمة"""
    print("\n📦 تثبيت المتطلبات المتقدمة...")
    
    try:
        # تثبيت من ملف المتطلبات المتقدمة
        if os.path.exists('requirements_advanced.txt'):
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements_advanced.txt'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ تم تثبيت جميع المتطلبات المتقدمة")
                return True
            else:
                print(f"❌ فشل في تثبيت بعض المتطلبات: {result.stderr}")
                return False
        else:
            print("❌ ملف requirements_advanced.txt غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def system_check():
    """فحص شامل للنظام"""
    print("\n🔍 فحص النظام...")
    print("-" * 40)
    
    # فحص Python
    python_ok = check_python_version()
    
    # فحص المكتبات
    print("\n📚 فحص المكتبات:")
    missing_basic, missing_advanced = check_required_modules()
    
    # فحص الملفات
    print("\n📁 فحص الملفات:")
    required_files = [
        'main.py',
        'advanced_main.py', 
        'requirements.txt',
        'requirements_advanced.txt',
        'src/app.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    # ملخص الفحص
    print("\n📊 ملخص الفحص:")
    print(f"Python: {'✅ مناسب' if python_ok else '❌ غير مناسب'}")
    print(f"المكتبات الأساسية: {'✅ متوفرة' if not missing_basic else f'❌ مفقود {len(missing_basic)}'}")
    print(f"المكتبات المتقدمة: {'✅ متوفرة' if not missing_advanced else f'⚠️ مفقود {len(missing_advanced)}'}")
    print(f"الملفات: {'✅ متوفرة' if not missing_files else f'❌ مفقود {len(missing_files)}'}")
    
    # التوصيات
    print("\n💡 التوصيات:")
    if missing_basic:
        print("- قم بتثبيت المكتبات الأساسية المفقودة")
    if missing_advanced:
        print("- قم بتثبيت المكتبات المتقدمة للحصول على جميع الميزات")
    if missing_files:
        print("- تأكد من وجود جميع ملفات المشروع")
    
    if python_ok and not missing_basic and not missing_files:
        print("✅ النظام جاهز لتشغيل النسخة الأساسية")
        if not missing_advanced:
            print("✅ النظام جاهز لتشغيل النسخة المتقدمة")

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من Python أولاً
        if not check_python_version():
            input("\nاضغط Enter للخروج...")
            return
        
        while True:
            choice = show_startup_options()
            
            if choice == 1:
                # تشغيل النسخة المتقدمة
                missing_basic, missing_advanced = check_required_modules()
                
                if missing_basic:
                    print("❌ المكتبات الأساسية مفقودة، لا يمكن تشغيل النسخة المتقدمة")
                    print("💡 جرب الخيار 3 لتثبيت المتطلبات أو الخيار 2 للنسخة الأساسية")
                    continue
                
                if missing_advanced:
                    print("⚠️ بعض المكتبات المتقدمة مفقودة، قد لا تعمل جميع الميزات")
                    response = input("هل تريد المتابعة؟ (y/n): ").lower()
                    if response not in ['y', 'yes', 'نعم']:
                        continue
                
                if run_advanced_version():
                    break
                    
            elif choice == 2:
                # تشغيل النسخة الأساسية
                missing_basic, _ = check_required_modules()
                
                if missing_basic:
                    print("❌ المكتبات الأساسية مفقودة")
                    response = input("هل تريد تثبيتها الآن؟ (y/n): ").lower()
                    if response in ['y', 'yes', 'نعم']:
                        if install_missing_modules(missing_basic):
                            print("✅ تم تثبيت المكتبات، جرب التشغيل مرة أخرى")
                        continue
                    else:
                        continue
                
                if run_basic_version():
                    break
                    
            elif choice == 3:
                # تثبيت المتطلبات المتقدمة
                install_advanced_requirements()
                input("\nاضغط Enter للمتابعة...")
                
            elif choice == 4:
                # فحص النظام
                system_check()
                input("\nاضغط Enter للمتابعة...")
                
            elif choice == 5:
                # خروج
                print("\n👋 شكراً لاستخدام شاشة أوقات الصلاة")
                break
    
    except KeyboardInterrupt:
        print("\n\n👋 تم إنهاء البرنامج")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
