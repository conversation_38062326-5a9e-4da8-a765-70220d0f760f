#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكون تذييل الصفحة - عرض النصوص الإسلامية
"""

import tkinter as tk
from tkinter import ttk
import arabic_reshaper
from bidi.algorithm import get_display

class FooterFrame(tk.Frame):
    """إطار تذييل الصفحة"""
    
    def __init__(self, parent, app):
        super().__init__(parent, bg='#8B0000')
        self.app = app
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة التذييل"""
        # إطار النص الإسلامي
        self.text_frame = tk.Frame(
            self,
            bg='#B22222',
            relief=tk.RAISED,
            bd=3
        )
        self.text_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # النص الإسلامي
        self.islamic_text_label = tk.Label(
            self.text_frame,
            text=self.format_arabic_text("وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا"),
            font=self.app.get_font('arabic'),
            bg='#B22222',
            fg='white',
            wraplength=1400,
            justify=tk.CENTER
        )
        self.islamic_text_label.pack(pady=15)
    
    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
    
    def update_text(self, new_text):
        """تحديث النص الإسلامي"""
        formatted_text = self.format_arabic_text(new_text)
        self.islamic_text_label.config(text=formatted_text)
        
        # تأثير الانتقال (تلاشي)
        self.fade_in_effect()
    
    def fade_in_effect(self):
        """تأثير الظهور التدريجي"""
        # تأثير بسيط للانتقال
        self.islamic_text_label.config(fg='#CCCCCC')
        self.after(100, lambda: self.islamic_text_label.config(fg='white'))
