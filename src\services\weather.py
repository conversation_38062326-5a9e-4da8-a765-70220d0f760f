#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خدمة الطقس
"""

import requests
import json
from datetime import datetime, timedelta

class WeatherService:
    """خدمة معلومات الطقس"""
    
    def __init__(self):
        self.latitude = 36.7538  # الجزائر العاصمة (افتراضي)
        self.longitude = 3.0588
        self.api_key = None  # مفتاح API (اختياري)
        self.cache_file = "weather_cache.json"
        self.last_update = None
        self.cached_weather = None
    
    def set_location(self, latitude, longitude):
        """تحديد الموقع الجغرافي"""
        self.latitude = latitude
        self.longitude = longitude
        self.cached_weather = None  # إعادة تعيين الكاش
    
    def get_weather(self):
        """الحصول على معلومات الطقس"""
        try:
            # التحقق من الكاش
            if self.is_cache_valid():
                return self.cached_weather
            
            # جلب البيانات من API مجاني
            weather = self.fetch_from_free_api()
            if weather:
                self.cached_weather = weather
                self.last_update = datetime.now()
                self.save_cache()
                return weather
            
            # في حالة فشل API، استخدام بيانات وهمية
            return self.get_mock_weather()
            
        except Exception as e:
            print(f"خطأ في جلب معلومات الطقس: {e}")
            return self.get_mock_weather()
    
    def fetch_from_free_api(self):
        """جلب البيانات من API مجاني (wttr.in)"""
        try:
            url = f"https://wttr.in/{self.latitude},{self.longitude}"
            params = {
                'format': 'j1',  # JSON format
                'lang': 'ar'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            current = data['current_condition'][0]
            
            weather_data = {
                'temperature': int(current['temp_C']),
                'feels_like': int(current['FeelsLikeC']),
                'humidity': int(current['humidity']),
                'condition': current['weatherDesc'][0]['value'],
                'icon': self.get_weather_icon(int(current['weatherCode'])),
                'city': data['nearest_area'][0]['areaName'][0]['value'],
                'country': data['nearest_area'][0]['country'][0]['value']
            }
            
            return weather_data
            
        except Exception as e:
            print(f"خطأ في جلب البيانات من wttr.in: {e}")
            return None
    
    def fetch_from_openweather(self):
        """جلب البيانات من OpenWeatherMap (يتطلب API key)"""
        if not self.api_key:
            return None
        
        try:
            url = "https://api.openweathermap.org/data/2.5/weather"
            params = {
                'lat': self.latitude,
                'lon': self.longitude,
                'appid': self.api_key,
                'units': 'metric',
                'lang': 'ar'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            weather_data = {
                'temperature': round(data['main']['temp']),
                'feels_like': round(data['main']['feels_like']),
                'humidity': data['main']['humidity'],
                'condition': data['weather'][0]['description'],
                'icon': data['weather'][0]['icon'],
                'city': data['name'],
                'country': data['sys']['country']
            }
            
            return weather_data
            
        except Exception as e:
            print(f"خطأ في جلب البيانات من OpenWeather: {e}")
            return None
    
    def get_mock_weather(self):
        """بيانات طقس وهمية للتجربة"""
        return {
            'temperature': 30,
            'feels_like': 28,
            'humidity': 41,
            'condition': 'صافي',
            'icon': '☀️',
            'city': 'الجزائر',
            'country': 'DZ'
        }
    
    def get_weather_icon(self, weather_code):
        """الحصول على رمز الطقس حسب الكود"""
        icons = {
            113: '☀️',  # Clear/Sunny
            116: '⛅',  # Partly cloudy
            119: '☁️',  # Cloudy
            122: '☁️',  # Overcast
            143: '🌫️',  # Mist
            176: '🌦️',  # Patchy rain possible
            179: '🌨️',  # Patchy snow possible
            182: '🌧️',  # Patchy sleet possible
            185: '🌧️',  # Patchy freezing drizzle possible
            200: '⛈️',  # Thundery outbreaks possible
            227: '❄️',  # Blowing snow
            230: '❄️',  # Blizzard
            248: '🌫️',  # Fog
            260: '🌫️',  # Freezing fog
            263: '🌦️',  # Patchy light drizzle
            266: '🌧️',  # Light drizzle
            281: '🌧️',  # Freezing drizzle
            284: '🌧️',  # Heavy freezing drizzle
            293: '🌦️',  # Patchy light rain
            296: '🌧️',  # Light rain
            299: '🌧️',  # Moderate rain at times
            302: '🌧️',  # Moderate rain
            305: '🌧️',  # Heavy rain at times
            308: '🌧️',  # Heavy rain
            311: '🌧️',  # Light freezing rain
            314: '🌧️',  # Moderate or heavy freezing rain
            317: '🌧️',  # Light sleet
            320: '🌧️',  # Moderate or heavy sleet
            323: '🌨️',  # Patchy light snow
            326: '❄️',  # Light snow
            329: '❄️',  # Patchy moderate snow
            332: '❄️',  # Moderate snow
            335: '❄️',  # Patchy heavy snow
            338: '❄️',  # Heavy snow
            350: '🌧️',  # Ice pellets
            353: '🌦️',  # Light rain shower
            356: '🌧️',  # Moderate or heavy rain shower
            359: '🌧️',  # Torrential rain shower
            362: '🌨️',  # Light sleet showers
            365: '🌨️',  # Moderate or heavy sleet showers
            368: '🌨️',  # Light snow showers
            371: '❄️',  # Moderate or heavy snow showers
            374: '🌧️',  # Light showers of ice pellets
            377: '🌧️',  # Moderate or heavy showers of ice pellets
            386: '⛈️',  # Patchy light rain with thunder
            389: '⛈️',  # Moderate or heavy rain with thunder
            392: '⛈️',  # Patchy light snow with thunder
            395: '⛈️',  # Moderate or heavy snow with thunder
        }
        
        return icons.get(weather_code, '☀️')
    
    def get_country_info(self):
        """الحصول على معلومات الدولة حسب الموقع"""
        # تحديد الدولة بناءً على الإحداثيات (مبسط)
        countries = {
            'DZ': {'name': 'الجزائر', 'flag': '🇩🇿'},
            'SA': {'name': 'السعودية', 'flag': '🇸🇦'},
            'EG': {'name': 'مصر', 'flag': '🇪🇬'},
            'MA': {'name': 'المغرب', 'flag': '🇲🇦'},
            'TN': {'name': 'تونس', 'flag': '🇹🇳'},
            'AE': {'name': 'الإمارات', 'flag': '🇦🇪'},
            'QA': {'name': 'قطر', 'flag': '🇶🇦'},
            'KW': {'name': 'الكويت', 'flag': '🇰🇼'},
            'BH': {'name': 'البحرين', 'flag': '🇧🇭'},
            'OM': {'name': 'عمان', 'flag': '🇴🇲'},
            'JO': {'name': 'الأردن', 'flag': '🇯🇴'},
            'LB': {'name': 'لبنان', 'flag': '🇱🇧'},
            'SY': {'name': 'سوريا', 'flag': '🇸🇾'},
            'IQ': {'name': 'العراق', 'flag': '🇮🇶'},
            'YE': {'name': 'اليمن', 'flag': '🇾🇪'},
            'LY': {'name': 'ليبيا', 'flag': '🇱🇾'},
            'SD': {'name': 'السودان', 'flag': '🇸🇩'},
        }
        
        # تحديد الدولة بناءً على الإحداثيات (مبسط جداً)
        if 18 <= self.latitude <= 37 and -9 <= self.longitude <= 12:
            return countries['DZ']
        elif 16 <= self.latitude <= 32 and 34 <= self.longitude <= 56:
            return countries['SA']
        elif 22 <= self.latitude <= 32 and 25 <= self.longitude <= 35:
            return countries['EG']
        else:
            return countries['DZ']  # افتراضي
    
    def is_cache_valid(self):
        """التحقق من صحة الكاش"""
        if not self.cached_weather or not self.last_update:
            return False
        
        # الكاش صالح لمدة 30 دقيقة
        time_diff = datetime.now() - self.last_update
        return time_diff.total_seconds() < 1800
    
    def save_cache(self):
        """حفظ الكاش في ملف"""
        try:
            cache_data = {
                'weather': self.cached_weather,
                'last_update': self.last_update.isoformat(),
                'location': {
                    'latitude': self.latitude,
                    'longitude': self.longitude
                }
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ كاش الطقس: {e}")
    
    def load_cache(self):
        """تحميل الكاش من ملف"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            self.cached_weather = cache_data['weather']
            self.last_update = datetime.fromisoformat(cache_data['last_update'])
            
            # التحقق من تطابق الموقع
            cached_location = cache_data.get('location', {})
            if (cached_location.get('latitude') != self.latitude or 
                cached_location.get('longitude') != self.longitude):
                self.cached_weather = None
                self.last_update = None
                
        except Exception as e:
            print(f"خطأ في تحميل كاش الطقس: {e}")
            self.cached_weather = None
            self.last_update = None
