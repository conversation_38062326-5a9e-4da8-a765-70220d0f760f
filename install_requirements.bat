@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   تثبيت متطلبات برنامج شاشة أوقات الصلاة
echo   Mosque Prayer Times Display Setup
echo ========================================
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo.
    echo يرجى تحميل وتثبيت Python من:
    echo https://www.python.org/downloads/
    echo.
    echo تأكد من اختيار "Add Python to PATH" أثناء التثبيت
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ تم العثور على Python %PYTHON_VERSION%

echo.
echo 🔄 تحديث pip...
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: فشل في تحديث pip
)

echo.
echo 📦 تثبيت المكتبات الأساسية...

echo   - تثبيت requests...
pip install requests>=2.31.0
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت requests
    goto :error
)

echo   - تثبيت pygame...
pip install pygame>=2.5.0
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت pygame
    goto :error
)

echo   - تثبيت Pillow...
pip install Pillow>=10.0.0
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Pillow
    goto :error
)

echo.
echo 📝 تثبيت مكتبات النصوص العربية...

echo   - تثبيت python-bidi...
pip install python-bidi>=0.4.2
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت python-bidi
    goto :error
)

echo   - تثبيت arabic-reshaper...
pip install arabic-reshaper>=3.0.0
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت arabic-reshaper
    goto :error
)

echo.
echo 📅 تثبيت مكتبات التاريخ والموقع...

echo   - تثبيت hijri-converter...
pip install hijri-converter>=2.3.1
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت hijri-converter
    goto :error
)

echo   - تثبيت geopy...
pip install geopy>=2.3.0
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت geopy
    goto :error
)

echo.
echo 🔊 تثبيت مكتبات الصوت...

echo   - تثبيت pyttsx3...
pip install pyttsx3>=2.90
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت pyttsx3
    goto :error
)

echo.
echo ⏰ تثبيت مكتبات الجدولة...

echo   - تثبيت schedule...
pip install schedule>=1.2.0
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت schedule
    goto :error
)

echo.
echo 🔧 تثبيت أدوات البناء (اختيارية)...

echo   - تثبيت pyinstaller...
pip install pyinstaller>=5.13.0
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: فشل في تثبيت pyinstaller (غير ضروري للتشغيل)
)

echo.
echo 📁 إنشاء المجلدات المطلوبة...

if not exist "audio" (
    mkdir audio
    echo ✅ تم إنشاء مجلد audio
)

if not exist "config" (
    mkdir config
    echo ✅ تم إنشاء مجلد config
)

echo.
echo 📄 إنشاء ملفات المساعدة...

echo # ملفات الأذان > audio\README.txt
echo. >> audio\README.txt
echo ضع ملفات الأذان هنا: >> audio\README.txt
echo - azan_fajr.mp3: أذان الفجر >> audio\README.txt
echo - azan_normal.mp3: أذان باقي الصلوات >> audio\README.txt
echo. >> audio\README.txt
echo التنسيقات المدعومة: MP3, WAV, OGG >> audio\README.txt

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح!
echo.
echo 🚀 يمكنك الآن تشغيل البرنامج:
echo.
echo   للنسخة المبسطة (موصى بها للبداية):
echo   python run_simple.py
echo.
echo   للنسخة الكاملة (جميع الميزات):
echo   python main.py
echo.
echo   لبناء ملف تنفيذي:
echo   python build_exe.py
echo.
echo 📖 للمساعدة والتوثيق:
echo   - اقرأ QUICK_START_PYTHON.md للبداية السريعة
echo   - اقرأ README_PYTHON.md للتوثيق الكامل
echo.
echo 🎮 اختصارات مفيدة:
echo   - اضغط A لفتح لوحة التحكم
echo   - اضغط F11 لملء الشاشة
echo   - اضغط Escape للخروج من ملء الشاشة
echo.
goto :success

:error
echo.
echo ❌ حدث خطأ أثناء التثبيت!
echo.
echo 🔧 حلول مقترحة:
echo   1. تأكد من اتصال الإنترنت
echo   2. شغل الملف كمدير (Run as Administrator)
echo   3. جرب الأمر: pip install --user [package-name]
echo   4. تحديث pip: python -m pip install --upgrade pip
echo.
echo 📞 للمساعدة:
echo   - راجع QUICK_START_PYTHON.md
echo   - تحقق من رسائل الخطأ أعلاه
echo.
pause
exit /b 1

:success
echo 🕌 بارك الله فيكم على استخدام هذا البرنامج
echo    في خدمة المجتمع الإسلامي
echo.
pause
