#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الإشعارات المتقدم
Advanced Notifications System
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from datetime import datetime, timedelta
import json
import pygame
import pyttsx3
from enum import Enum
from typing import Dict, List, Optional, Callable

class NotificationType(Enum):
    """أنواع الإشعارات"""
    PRAYER_REMINDER = "prayer_reminder"
    PRAYER_TIME = "prayer_time"
    PRAYER_IQAMA = "prayer_iqama"
    SPECIAL_EVENT = "special_event"
    SYSTEM_ALERT = "system_alert"
    MAINTENANCE = "maintenance"
    ANNOUNCEMENT = "announcement"

class NotificationPriority(Enum):
    """أولوية الإشعارات"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class NotificationMethod(Enum):
    """طرق الإشعار"""
    VISUAL = "visual"
    AUDIO = "audio"
    VIBRATION = "vibration"
    FLASH = "flash"
    POPUP = "popup"

class Notification:
    """كلاس الإشعار"""
    
    def __init__(self, 
                 notification_id: str,
                 title: str,
                 message: str,
                 notification_type: NotificationType,
                 priority: NotificationPriority = NotificationPriority.NORMAL,
                 methods: List[NotificationMethod] = None,
                 duration: int = 5,
                 auto_dismiss: bool = True,
                 callback: Optional[Callable] = None,
                 data: Dict = None):
        
        self.id = notification_id
        self.title = title
        self.message = message
        self.type = notification_type
        self.priority = priority
        self.methods = methods or [NotificationMethod.VISUAL, NotificationMethod.AUDIO]
        self.duration = duration
        self.auto_dismiss = auto_dismiss
        self.callback = callback
        self.data = data or {}
        self.created_at = datetime.now()
        self.shown_at = None
        self.dismissed_at = None
        self.is_active = False

class AdvancedNotificationSystem:
    """نظام الإشعارات المتقدم"""
    
    def __init__(self, parent_window):
        self.parent = parent_window
        self.notifications_queue = []
        self.active_notifications = {}
        self.notification_history = []
        self.settings = self.load_settings()
        
        # مكونات النظام
        self.tts_engine = None
        self.sound_system = None
        self.visual_components = {}
        
        # خيوط العمل
        self.notification_thread = None
        self.is_running = True
        
        self.initialize_systems()
        self.start_notification_processor()
    
    def initialize_systems(self):
        """تهيئة أنظمة الإشعارات"""
        try:
            # تهيئة نظام النطق
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 150)
            self.tts_engine.setProperty('volume', self.settings.get('tts_volume', 0.8))
            
            # تهيئة نظام الصوت
            pygame.mixer.init()
            
            print("✅ تم تهيئة أنظمة الإشعارات")
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة أنظمة الإشعارات: {e}")
    
    def load_settings(self) -> Dict:
        """تحميل إعدادات الإشعارات"""
        default_settings = {
            "enabled": True,
            "visual_enabled": True,
            "audio_enabled": True,
            "tts_enabled": True,
            "flash_enabled": True,
            "popup_enabled": True,
            "volume": 0.8,
            "tts_volume": 0.8,
            "reminder_times": [15, 10, 5, 1],  # دقائق قبل الأذان
            "iqama_reminder": 2,  # دقائق قبل الإقامة
            "flash_duration": 3,
            "popup_duration": 5,
            "priority_override": True,
            "quiet_hours": {
                "enabled": False,
                "start": "22:00",
                "end": "06:00"
            }
        }
        
        try:
            with open('config/notification_settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
                # دمج مع الإعدادات الافتراضية
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
        except:
            return default_settings
    
    def save_settings(self):
        """حفظ إعدادات الإشعارات"""
        try:
            with open('config/notification_settings.json', 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الإشعارات: {e}")
    
    def add_notification(self, notification: Notification):
        """إضافة إشعار جديد"""
        if not self.settings.get('enabled', True):
            return
        
        # التحقق من الساعات الهادئة
        if self.is_quiet_hours() and notification.priority != NotificationPriority.CRITICAL:
            return
        
        # إضافة للطابور
        self.notifications_queue.append(notification)
        self.notifications_queue.sort(key=lambda x: x.priority.value, reverse=True)
        
        print(f"📢 تم إضافة إشعار: {notification.title}")
    
    def is_quiet_hours(self) -> bool:
        """التحقق من الساعات الهادئة"""
        quiet_settings = self.settings.get('quiet_hours', {})
        if not quiet_settings.get('enabled', False):
            return False
        
        now = datetime.now().time()
        start_time = datetime.strptime(quiet_settings['start'], '%H:%M').time()
        end_time = datetime.strptime(quiet_settings['end'], '%H:%M').time()
        
        if start_time <= end_time:
            return start_time <= now <= end_time
        else:  # عبر منتصف الليل
            return now >= start_time or now <= end_time
    
    def start_notification_processor(self):
        """بدء معالج الإشعارات"""
        self.notification_thread = threading.Thread(
            target=self.process_notifications, 
            daemon=True
        )
        self.notification_thread.start()
    
    def process_notifications(self):
        """معالجة الإشعارات"""
        while self.is_running:
            try:
                if self.notifications_queue:
                    notification = self.notifications_queue.pop(0)
                    self.show_notification(notification)
                
                # تنظيف الإشعارات المنتهية
                self.cleanup_expired_notifications()
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"خطأ في معالجة الإشعارات: {e}")
                time.sleep(1)
    
    def show_notification(self, notification: Notification):
        """عرض الإشعار"""
        try:
            notification.shown_at = datetime.now()
            notification.is_active = True
            self.active_notifications[notification.id] = notification
            
            # عرض حسب الطرق المحددة
            for method in notification.methods:
                if method == NotificationMethod.VISUAL:
                    self.show_visual_notification(notification)
                elif method == NotificationMethod.AUDIO:
                    self.play_audio_notification(notification)
                elif method == NotificationMethod.POPUP:
                    self.show_popup_notification(notification)
                elif method == NotificationMethod.FLASH:
                    self.flash_screen(notification)
            
            # جدولة الإزالة التلقائية
            if notification.auto_dismiss:
                threading.Timer(
                    notification.duration,
                    self.dismiss_notification,
                    args=[notification.id]
                ).start()
            
            # إضافة للتاريخ
            self.notification_history.append(notification)
            
            print(f"📢 تم عرض إشعار: {notification.title}")
            
        except Exception as e:
            print(f"خطأ في عرض الإشعار: {e}")
    
    def show_visual_notification(self, notification: Notification):
        """عرض الإشعار المرئي"""
        try:
            # إنشاء نافذة الإشعار
            notification_window = tk.Toplevel(self.parent)
            notification_window.title("إشعار")
            notification_window.geometry("400x150")
            notification_window.configure(bg=self.get_notification_color(notification.priority))
            
            # جعل النافذة في المقدمة
            notification_window.attributes('-topmost', True)
            notification_window.attributes('-alpha', 0.95)
            
            # توسيط النافذة
            self.center_window(notification_window, 400, 150)
            
            # محتوى الإشعار
            title_label = tk.Label(
                notification_window,
                text=notification.title,
                font=('Arial', 16, 'bold'),
                bg=self.get_notification_color(notification.priority),
                fg='white'
            )
            title_label.pack(pady=10)
            
            message_label = tk.Label(
                notification_window,
                text=notification.message,
                font=('Arial', 12),
                bg=self.get_notification_color(notification.priority),
                fg='white',
                wraplength=350,
                justify=tk.CENTER
            )
            message_label.pack(pady=5)
            
            # زر الإغلاق
            close_button = tk.Button(
                notification_window,
                text="إغلاق",
                command=lambda: self.dismiss_notification_window(notification.id, notification_window),
                bg='white',
                fg='black',
                font=('Arial', 10)
            )
            close_button.pack(pady=10)
            
            # حفظ مرجع النافذة
            self.visual_components[notification.id] = notification_window
            
        except Exception as e:
            print(f"خطأ في عرض الإشعار المرئي: {e}")
    
    def play_audio_notification(self, notification: Notification):
        """تشغيل الإشعار الصوتي"""
        try:
            if not self.settings.get('audio_enabled', True):
                return
            
            # تشغيل صوت حسب نوع الإشعار
            sound_file = self.get_notification_sound(notification.type)
            
            if sound_file:
                pygame.mixer.music.load(sound_file)
                pygame.mixer.music.set_volume(self.settings.get('volume', 0.8))
                pygame.mixer.music.play()
            
            # نطق النص إذا كان مفعلاً
            if self.settings.get('tts_enabled', True) and self.tts_engine:
                threading.Thread(
                    target=self.speak_notification,
                    args=[notification],
                    daemon=True
                ).start()
                
        except Exception as e:
            print(f"خطأ في تشغيل الإشعار الصوتي: {e}")
    
    def speak_notification(self, notification: Notification):
        """نطق الإشعار"""
        try:
            text = f"{notification.title}. {notification.message}"
            self.tts_engine.say(text)
            self.tts_engine.runAndWait()
        except Exception as e:
            print(f"خطأ في نطق الإشعار: {e}")
    
    def show_popup_notification(self, notification: Notification):
        """عرض إشعار منبثق"""
        try:
            # إشعار منبثق في الزاوية
            popup = tk.Toplevel(self.parent)
            popup.title("")
            popup.geometry("300x100")
            popup.configure(bg=self.get_notification_color(notification.priority))
            popup.overrideredirect(True)  # بدون شريط العنوان
            
            # موضع في الزاوية اليمنى السفلى
            screen_width = popup.winfo_screenwidth()
            screen_height = popup.winfo_screenheight()
            x = screen_width - 320
            y = screen_height - 150
            popup.geometry(f"300x100+{x}+{y}")
            
            # محتوى الإشعار
            content_frame = tk.Frame(popup, bg=self.get_notification_color(notification.priority))
            content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            title_label = tk.Label(
                content_frame,
                text=notification.title,
                font=('Arial', 12, 'bold'),
                bg=self.get_notification_color(notification.priority),
                fg='white'
            )
            title_label.pack()
            
            message_label = tk.Label(
                content_frame,
                text=notification.message,
                font=('Arial', 10),
                bg=self.get_notification_color(notification.priority),
                fg='white',
                wraplength=280
            )
            message_label.pack()
            
            # تأثير الظهور
            self.animate_popup(popup)
            
            # حفظ مرجع النافذة
            self.visual_components[f"{notification.id}_popup"] = popup
            
        except Exception as e:
            print(f"خطأ في عرض الإشعار المنبثق: {e}")
    
    def flash_screen(self, notification: Notification):
        """وميض الشاشة"""
        try:
            if not self.settings.get('flash_enabled', True):
                return
            
            # إنشاء نافذة وميض
            flash_window = tk.Toplevel(self.parent)
            flash_window.attributes('-fullscreen', True)
            flash_window.attributes('-alpha', 0.3)
            flash_window.configure(bg=self.get_notification_color(notification.priority))
            flash_window.overrideredirect(True)
            
            # تأثير الوميض
            def flash_effect():
                for i in range(6):  # 3 ومضات
                    flash_window.attributes('-alpha', 0.5 if i % 2 == 0 else 0.1)
                    time.sleep(0.2)
                flash_window.destroy()
            
            threading.Thread(target=flash_effect, daemon=True).start()
            
        except Exception as e:
            print(f"خطأ في وميض الشاشة: {e}")
    
    def get_notification_color(self, priority: NotificationPriority) -> str:
        """الحصول على لون الإشعار حسب الأولوية"""
        colors = {
            NotificationPriority.LOW: '#4CAF50',      # أخضر
            NotificationPriority.NORMAL: '#2196F3',   # أزرق
            NotificationPriority.HIGH: '#FF9800',     # برتقالي
            NotificationPriority.CRITICAL: '#F44336'  # أحمر
        }
        return colors.get(priority, '#2196F3')
    
    def get_notification_sound(self, notification_type: NotificationType) -> str:
        """الحصول على ملف الصوت حسب نوع الإشعار"""
        sounds = {
            NotificationType.PRAYER_REMINDER: 'audio/reminder.mp3',
            NotificationType.PRAYER_TIME: 'audio/azan_normal.mp3',
            NotificationType.PRAYER_IQAMA: 'audio/iqama.mp3',
            NotificationType.SPECIAL_EVENT: 'audio/event.mp3',
            NotificationType.SYSTEM_ALERT: 'audio/alert.mp3',
            NotificationType.ANNOUNCEMENT: 'audio/announcement.mp3'
        }
        
        sound_file = sounds.get(notification_type, 'audio/notification.mp3')
        return sound_file if self.file_exists(sound_file) else None
    
    def file_exists(self, file_path: str) -> bool:
        """التحقق من وجود الملف"""
        try:
            import os
            return os.path.exists(file_path)
        except:
            return False
    
    def center_window(self, window, width, height):
        """توسيط النافذة"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    def animate_popup(self, popup):
        """تحريك الإشعار المنبثق"""
        try:
            # تأثير الانزلاق من الأسفل
            start_y = popup.winfo_screenheight()
            end_y = popup.winfo_screenheight() - 150
            
            def slide_up():
                current_y = start_y
                while current_y > end_y:
                    current_y -= 10
                    popup.geometry(f"300x100+{popup.winfo_screenwidth()-320}+{current_y}")
                    popup.update()
                    time.sleep(0.02)
            
            threading.Thread(target=slide_up, daemon=True).start()
            
        except Exception as e:
            print(f"خطأ في تحريك الإشعار: {e}")
    
    def dismiss_notification(self, notification_id: str):
        """إزالة الإشعار"""
        try:
            if notification_id in self.active_notifications:
                notification = self.active_notifications[notification_id]
                notification.dismissed_at = datetime.now()
                notification.is_active = False
                
                # إزالة المكونات المرئية
                if notification_id in self.visual_components:
                    window = self.visual_components[notification_id]
                    if window.winfo_exists():
                        window.destroy()
                    del self.visual_components[notification_id]
                
                # إزالة الإشعار المنبثق
                popup_key = f"{notification_id}_popup"
                if popup_key in self.visual_components:
                    popup = self.visual_components[popup_key]
                    if popup.winfo_exists():
                        popup.destroy()
                    del self.visual_components[popup_key]
                
                del self.active_notifications[notification_id]
                
                print(f"✅ تم إزالة الإشعار: {notification.title}")
                
        except Exception as e:
            print(f"خطأ في إزالة الإشعار: {e}")
    
    def dismiss_notification_window(self, notification_id: str, window):
        """إزالة نافذة الإشعار"""
        try:
            window.destroy()
            self.dismiss_notification(notification_id)
        except Exception as e:
            print(f"خطأ في إزالة نافذة الإشعار: {e}")
    
    def cleanup_expired_notifications(self):
        """تنظيف الإشعارات المنتهية"""
        try:
            current_time = datetime.now()
            expired_ids = []
            
            for notification_id, notification in self.active_notifications.items():
                if notification.auto_dismiss and notification.shown_at:
                    elapsed = (current_time - notification.shown_at).total_seconds()
                    if elapsed > notification.duration:
                        expired_ids.append(notification_id)
            
            for notification_id in expired_ids:
                self.dismiss_notification(notification_id)
                
        except Exception as e:
            print(f"خطأ في تنظيف الإشعارات: {e}")
    
    def create_prayer_reminders(self, prayer_times: Dict):
        """إنشاء تذكيرات الصلاة"""
        try:
            reminder_times = self.settings.get('reminder_times', [15, 10, 5, 1])
            
            for prayer_name, times in prayer_times.items():
                if prayer_name == 'sunrise':  # لا تذكير للشروق
                    continue
                
                azan_time = times.get('azan')
                if not azan_time:
                    continue
                
                # تذكيرات قبل الأذان
                for minutes_before in reminder_times:
                    reminder_time = self.calculate_reminder_time(azan_time, minutes_before)
                    
                    notification = Notification(
                        notification_id=f"reminder_{prayer_name}_{minutes_before}",
                        title=f"تذكير صلاة {self.get_prayer_name(prayer_name)}",
                        message=f"باقي {minutes_before} دقيقة على الأذان",
                        notification_type=NotificationType.PRAYER_REMINDER,
                        priority=NotificationPriority.NORMAL,
                        methods=[NotificationMethod.POPUP, NotificationMethod.AUDIO],
                        duration=3
                    )
                    
                    self.schedule_notification(notification, reminder_time)
                
                # إشعار وقت الأذان
                azan_notification = Notification(
                    notification_id=f"azan_{prayer_name}",
                    title=f"حان الآن موعد صلاة {self.get_prayer_name(prayer_name)}",
                    message="الله أكبر الله أكبر",
                    notification_type=NotificationType.PRAYER_TIME,
                    priority=NotificationPriority.HIGH,
                    methods=[NotificationMethod.VISUAL, NotificationMethod.AUDIO, NotificationMethod.FLASH],
                    duration=10
                )
                
                self.schedule_notification(azan_notification, azan_time)
                
                # تذكير الإقامة
                iqama_time = times.get('iqama')
                if iqama_time:
                    iqama_reminder_time = self.calculate_reminder_time(
                        iqama_time, 
                        self.settings.get('iqama_reminder', 2)
                    )
                    
                    iqama_notification = Notification(
                        notification_id=f"iqama_reminder_{prayer_name}",
                        title=f"تذكير إقامة صلاة {self.get_prayer_name(prayer_name)}",
                        message=f"باقي {self.settings.get('iqama_reminder', 2)} دقيقة على الإقامة",
                        notification_type=NotificationType.PRAYER_IQAMA,
                        priority=NotificationPriority.HIGH,
                        methods=[NotificationMethod.POPUP, NotificationMethod.AUDIO],
                        duration=5
                    )
                    
                    self.schedule_notification(iqama_notification, iqama_reminder_time)
            
            print("✅ تم إنشاء تذكيرات الصلاة")
            
        except Exception as e:
            print(f"خطأ في إنشاء تذكيرات الصلاة: {e}")
    
    def calculate_reminder_time(self, prayer_time: str, minutes_before: int) -> datetime:
        """حساب وقت التذكير"""
        try:
            hour, minute = map(int, prayer_time.split(':'))
            prayer_datetime = datetime.now().replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # إذا كان الوقت قد مضى اليوم، اجعله غداً
            if prayer_datetime <= datetime.now():
                prayer_datetime += timedelta(days=1)
            
            reminder_datetime = prayer_datetime - timedelta(minutes=minutes_before)
            return reminder_datetime
            
        except Exception as e:
            print(f"خطأ في حساب وقت التذكير: {e}")
            return datetime.now()
    
    def schedule_notification(self, notification: Notification, scheduled_time: datetime):
        """جدولة الإشعار"""
        try:
            delay = (scheduled_time - datetime.now()).total_seconds()
            
            if delay > 0:
                threading.Timer(
                    delay,
                    self.add_notification,
                    args=[notification]
                ).start()
                
                print(f"📅 تم جدولة إشعار: {notification.title} في {scheduled_time.strftime('%H:%M')}")
            
        except Exception as e:
            print(f"خطأ في جدولة الإشعار: {e}")
    
    def get_prayer_name(self, prayer_key: str) -> str:
        """الحصول على اسم الصلاة بالعربية"""
        names = {
            'fajr': 'الفجر',
            'dhuhr': 'الظهر',
            'asr': 'العصر',
            'maghrib': 'المغرب',
            'isha': 'العشاء'
        }
        return names.get(prayer_key, prayer_key)
    
    def stop(self):
        """إيقاف نظام الإشعارات"""
        self.is_running = False
        
        # إزالة جميع الإشعارات النشطة
        for notification_id in list(self.active_notifications.keys()):
            self.dismiss_notification(notification_id)
        
        print("🛑 تم إيقاف نظام الإشعارات")
