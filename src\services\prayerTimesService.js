// Prayer Times Service using AlAdhan API
export const getPrayerTimes = async (latitude, longitude) => {
  try {
    const today = new Date();
    const timestamp = Math.floor(today.getTime() / 1000);
    
    const response = await fetch(
      `https://api.aladhan.com/v1/timings/${timestamp}?latitude=${latitude}&longitude=${longitude}&method=2`
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch prayer times');
    }
    
    const data = await response.json();
    const timings = data.data.timings;
    
    // Convert to 24-hour format and add iqama times (10 minutes after azan for most prayers)
    const prayerTimes = {
      fajr: {
        azan: convertTo24Hour(timings.Fajr),
        iqama: addMinutes(convertTo24Hour(timings.Fajr), 10)
      },
      sunrise: {
        azan: convertTo24Hour(timings.Sunrise),
        iqama: convertTo24Hour(timings.Sunrise) // No iqama for sunrise
      },
      dhuhr: {
        azan: convertTo24Hour(timings.Dhuhr),
        iqama: addMinutes(convertTo24Hour(timings.Dhuhr), 10)
      },
      asr: {
        azan: convertTo24Hour(timings.Asr),
        iqama: addMinutes(convertTo24Hour(timings.Asr), 10)
      },
      maghrib: {
        azan: convertTo24Hour(timings.Maghrib),
        iqama: addMinutes(convertTo24Hour(timings.Maghrib), 10)
      },
      isha: {
        azan: convertTo24Hour(timings.Isha),
        iqama: addMinutes(convertTo24Hour(timings.Isha), 10)
      }
    };
    
    return prayerTimes;
  } catch (error) {
    console.error('Error fetching prayer times:', error);
    // Return default times if API fails
    return getDefaultPrayerTimes();
  }
};

const convertTo24Hour = (time12h) => {
  const [time, modifier] = time12h.split(' ');
  let [hours, minutes] = time.split(':');
  
  if (hours === '12') {
    hours = '00';
  }
  
  if (modifier === 'PM') {
    hours = parseInt(hours, 10) + 12;
  }
  
  return `${hours.toString().padStart(2, '0')}:${minutes}`;
};

const addMinutes = (timeStr, minutesToAdd) => {
  const [hours, minutes] = timeStr.split(':').map(Number);
  const totalMinutes = hours * 60 + minutes + minutesToAdd;
  const newHours = Math.floor(totalMinutes / 60) % 24;
  const newMinutes = totalMinutes % 60;
  
  return `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}`;
};

const getDefaultPrayerTimes = () => {
  return {
    fajr: { azan: '04:11', iqama: '04:21' },
    sunrise: { azan: '05:37', iqama: '05:52' },
    dhuhr: { azan: '12:20', iqama: '12:30' },
    asr: { azan: '15:37', iqama: '15:47' },
    maghrib: { azan: '19:01', iqama: '19:11' },
    isha: { azan: '20:31', iqama: '20:41' }
  };
};

// Calculate prayer times locally using astronomical calculations
export const calculatePrayerTimesLocally = (latitude, longitude, date = new Date()) => {
  // This is a simplified calculation - for production use a proper library like PrayTimes.js
  const times = getDefaultPrayerTimes();
  
  // Adjust times based on latitude (very basic adjustment)
  const latitudeAdjustment = (latitude - 35) * 2; // minutes
  
  Object.keys(times).forEach(prayer => {
    if (prayer !== 'sunrise') {
      times[prayer].azan = addMinutes(times[prayer].azan, latitudeAdjustment);
      times[prayer].iqama = addMinutes(times[prayer].iqama, latitudeAdjustment);
    }
  });
  
  return times;
};
