* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', '<PERSON><PERSON>', sans-serif;
  direction: rtl;
  text-align: right;
  overflow: hidden;
  background: linear-gradient(135deg, #8B0000, #DC143C, #B22222);
  color: white;
  height: 100vh;
  width: 100vw;
}

#root {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

.app {
  height: 100vh;
  width: 100vw;
  position: relative;
  background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
  background-size: 400% 400%;
  animation: gradientShift 10s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.decorative-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
    radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 1px, transparent 1px),
    radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 2px, transparent 2px);
  background-size: 50px 50px, 60px 60px, 80px 80px;
  pointer-events: none;
}

.islamic-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  pointer-events: none;
}

.main-container {
  position: relative;
  z-index: 1;
  height: 100vh;
  display: grid;
  grid-template-rows: auto 1fr auto;
  padding: 20px;
  gap: 20px;
}

.header-section {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 20px;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1.2rem;
}

.flag-icon {
  width: 40px;
  height: 30px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.date-info {
  text-align: center;
  font-size: 1.1rem;
  line-height: 1.4;
}

.mosque-name {
  text-align: left;
  font-size: 1.3rem;
  font-weight: 600;
}

.main-content {
  display: grid;
  grid-template-columns: 200px 1fr 200px;
  align-items: center;
  gap: 40px;
  height: 100%;
}

.center-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.countdown-circle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255,255,255,0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.time-display {
  text-align: center;
}

.current-time {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  margin-bottom: 10px;
}

.prayer-times-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 15px;
  text-align: center;
  margin-top: 20px;
  width: 100%;
  max-width: 800px;
}

.prayer-time-card {
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px 10px;
  border: 2px solid rgba(255,255,255,0.2);
  box-shadow: 0 4px 16px rgba(0,0,0,0.2);
}

.prayer-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.prayer-time {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 4px;
}

.iqama-time {
  font-size: 1rem;
  opacity: 0.9;
}

.footer-section {
  text-align: center;
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px;
  border: 2px solid rgba(255,255,255,0.2);
}

.islamic-text {
  font-size: 1.2rem;
  line-height: 1.6;
  font-family: 'Amiri', serif;
}

.text-fade-in {
  animation: fadeIn 2s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.glow {
  text-shadow: 0 0 10px rgba(255,255,255,0.5);
}

@media (max-width: 1200px) {
  .main-container {
    padding: 15px;
    gap: 15px;
  }
  
  .current-time {
    font-size: 3rem;
  }
  
  .countdown-circle {
    width: 150px;
    height: 150px;
  }
  
  .prayer-times-grid {
    gap: 15px;
  }
}
