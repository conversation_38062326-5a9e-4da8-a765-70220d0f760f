#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج شاشة أوقات الصلاة للمسجد
Mosque Prayer Times Display

تطوير: Augment Agent
التاريخ: 2025
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
from pathlib import Path

# إضافة مجلد المشروع إلى المسار
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.app import MosquePrayerApp

def main():
    """تشغيل التطبيق الرئيسي"""
    try:
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        
        # إعداد النافذة
        root.title("شاشة أوقات الصلاة - المسجد")
        root.geometry("1920x1080")
        root.state('zoomed')  # ملء الشاشة على Windows
        root.configure(bg='#8B0000')
        
        # منع تغيير حجم النافذة
        root.resizable(False, False)
        
        # إخفاء شريط العنوان (وضع ملء الشاشة)
        root.overrideredirect(True)
        
        # إنشاء التطبيق
        app = MosquePrayerApp(root)
        
        # تشغيل التطبيق
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
