import React from 'react';
import { formatHijriDate, formatGregorianDate } from '../utils/dateUtils';

const Header = ({ weatherData, currentTime, location }) => {
  const getCountryFlag = (lat, lng) => {
    // Simple country detection based on coordinates
    // Algeria
    if (lat >= 18 && lat <= 37 && lng >= -9 && lng <= 12) {
      return '🇩🇿';
    }
    // Saudi Arabia
    if (lat >= 16 && lat <= 32 && lng >= 34 && lng <= 56) {
      return '🇸🇦';
    }
    // Egypt
    if (lat >= 22 && lat <= 32 && lng >= 25 && lng <= 35) {
      return '🇪🇬';
    }
    // Morocco
    if (lat >= 27 && lat <= 36 && lng >= -17 && lng <= -1) {
      return '🇲🇦';
    }
    // Tunisia
    if (lat >= 30 && lat <= 38 && lng >= 7 && lng <= 12) {
      return '🇹🇳';
    }
    // Default
    return '🌍';
  };

  const getWeatherIcon = (condition) => {
    if (!condition) return '☀️';
    
    const conditionLower = condition.toLowerCase();
    if (conditionLower.includes('clear') || conditionLower.includes('sunny')) return '☀️';
    if (conditionLower.includes('cloud')) return '☁️';
    if (conditionLower.includes('rain')) return '🌧️';
    if (conditionLower.includes('storm')) return '⛈️';
    if (conditionLower.includes('snow')) return '❄️';
    if (conditionLower.includes('fog') || conditionLower.includes('mist')) return '🌫️';
    
    return '☀️';
  };

  return (
    <div className="header-section">
      <div className="weather-info">
        <span className="flag-icon">{getCountryFlag(location.latitude, location.longitude)}</span>
        <div>
          <div>{getWeatherIcon(weatherData?.condition)} {weatherData?.temperature || '30'}°</div>
          <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>
            {weatherData?.humidity || '41'}° {weatherData?.feelsLike || '28'}°
          </div>
        </div>
      </div>
      
      <div className="date-info">
        <div>{formatGregorianDate(currentTime)}</div>
        <div>{formatHijriDate(currentTime)}</div>
      </div>
      
      <div className="mosque-name">
        اسم المسجد
      </div>
    </div>
  );
};

export default Header;
