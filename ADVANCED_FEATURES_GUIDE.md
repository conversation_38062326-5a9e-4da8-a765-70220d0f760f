# دليل الميزات المتقدمة
## Advanced Features Guide

# 🚀 شاشة أوقات الصلاة للمسجد - النسخة المتقدمة

## 📋 نظرة عامة

تم تطوير النسخة المتقدمة من شاشة أوقات الصلاة لتشمل مجموعة شاملة من الميزات المتطورة التي تجعل من إدارة المسجد أمراً سهلاً وفعالاً.

## 🎯 الميزات المتقدمة الجديدة

### 1. 🔔 نظام الإشعارات المتقدم
**الملف:** `src/services/advanced_notifications.py`

#### الميزات:
- **إشعارات متعددة المستويات**: عادي، مهم، عاجل، حرج
- **طرق إشعار متنوعة**: مرئي، صوتي، وميض، منبثق
- **تذكيرات الصلاة**: قابلة للتخصيص (15، 10، 5، 1 دقيقة قبل الأذان)
- **إشعارات الإقامة**: تذكير قبل الإقامة بدقيقتين
- **الساعات الهادئة**: إيقاف الإشعارات في أوقات محددة
- **نطق النصوص**: تحويل النص إلى كلام باللغة العربية

#### الاستخدام:
```python
from src.services.advanced_notifications import AdvancedNotificationSystem, Notification, NotificationType

# إنشاء نظام الإشعارات
notification_system = AdvancedNotificationSystem(parent_window)

# إضافة إشعار
notification = Notification(
    "prayer_reminder",
    "تذكير صلاة الظهر",
    "باقي 5 دقائق على الأذان",
    NotificationType.PRAYER_REMINDER
)
notification_system.add_notification(notification)
```

### 2. 📱 نظام إدارة المحتوى الديناميكي
**الملف:** `src/services/content_management.py`

#### الميزات:
- **أنواع محتوى متعددة**: نص، صورة، فيديو، إعلان
- **جدولة العرض**: تحديد أوقات بداية ونهاية المحتوى
- **أولوية المحتوى**: ترتيب العرض حسب الأهمية
- **تكرار المحتوى**: جدولة يومية، أسبوعية، شهرية
- **إدارة الملفات**: تحقق من صحة الصور والفيديوهات
- **عرض تلقائي**: تناوب المحتوى كل 15 ثانية

#### الاستخدام:
```python
from src.services.content_management import DynamicContentManager, ContentItem, ContentType

# إنشاء مدير المحتوى
content_manager = DynamicContentManager(parent_window)

# إضافة محتوى صورة
content_item = ContentItem(
    "image_1",
    "صورة المسجد",
    ContentType.IMAGE,
    "content/images/mosque.jpg",
    duration=30
)
content_manager.add_content_item(content_item)
```

### 3. 🌙 نظام التقويم الإسلامي المتقدم
**الملف:** `src/services/islamic_calendar.py`

#### الميزات:
- **التقويم الهجري**: تحويل دقيق للتواريخ
- **المناسبات الإسلامية**: رأس السنة، عاشوراء، المولد النبوي، الإسراء والمعراج
- **أحداث رمضان**: بداية الشهر، ليلة القدر، عيد الفطر
- **أحداث الحج**: يوم عرفة، عيد الأضحى
- **تذكيرات المناسبات**: إشعارات قبل 7، 3، 1 أيام
- **أحداث مخصصة**: إضافة مناسبات خاصة بالمسجد

#### الاستخدام:
```python
from src.services.islamic_calendar import AdvancedIslamicCalendar

# إنشاء نظام التقويم
calendar_system = AdvancedIslamicCalendar(parent_window)

# الحصول على التاريخ الهجري
hijri_date = calendar_system.get_current_hijri_date()

# الحصول على الأحداث الحالية
current_events = calendar_system.get_current_events_display()
```

### 4. 📊 نظام الإحصائيات والتقارير
**الملف:** `src/services/analytics_system.py`

#### الميزات:
- **مراقبة الأداء**: استخدام المعالج والذاكرة والقرص
- **إحصائيات الاستخدام**: عدد المستخدمين، الأخطاء، وقت الاستجابة
- **تقارير دورية**: يومية، أسبوعية، شهرية، سنوية
- **رسوم بيانية**: مخططات الأداء والاتجاهات
- **تحذيرات الأداء**: إنذارات عند تجاوز الحدود المسموحة
- **تصدير البيانات**: حفظ التقارير كملفات CSV

#### الاستخدام:
```python
from src.services.analytics_system import AdvancedAnalyticsSystem, MetricType

# إنشاء نظام الإحصائيات
analytics_system = AdvancedAnalyticsSystem(parent_window)

# تسجيل حدث
analytics_system.record_event(
    "user_login",
    MetricType.USER_INTERACTION,
    {"user_id": "admin", "action": "login"}
)

# إنشاء تقرير يومي
daily_report = analytics_system.generate_daily_report()
```

### 5. 🌐 نظام التحكم عن بُعد
**الملف:** `src/services/remote_control.py`

#### الميزات:
- **واجهة ويب**: لوحة تحكم عبر المتصفح
- **مصادقة آمنة**: نظام مستخدمين مع كلمات مرور مشفرة
- **أدوار المستخدمين**: إداري، مشرف، مشاهد
- **أوامر متنوعة**: تحديث أوقات الصلاة، تغيير الصوت، إرسال إعلانات
- **مراقبة الحالة**: عرض حالة النظام في الوقت الفعلي
- **أمان متقدم**: SSL، جلسات محدودة الوقت، حماية من الهجمات

#### الاستخدام:
```python
from src.services.remote_control import RemoteControlSystem

# إنشاء نظام التحكم عن بُعد
remote_system = RemoteControlSystem(parent_app)

# بدء الخادم
remote_system.start_server()

# الوصول عبر المتصفح: http://localhost:8080
# اسم المستخدم: admin
# كلمة المرور: admin123
```

## 🛠️ التثبيت والإعداد

### 1. متطلبات النظام
- **Python**: 3.8 أو أحدث
- **نظام التشغيل**: Windows 10/11، Linux، macOS
- **الذاكرة**: 4 GB RAM (8 GB مستحسن)
- **التخزين**: 2 GB مساحة فارغة

### 2. تثبيت المتطلبات

#### التثبيت الأساسي:
```bash
pip install -r requirements.txt
```

#### التثبيت المتقدم:
```bash
pip install -r requirements_advanced.txt
```

#### التثبيت التدريجي:
```bash
# المكتبات الأساسية
pip install tkinter requests pygame Pillow hijri-converter pyttsx3

# مكتبات الإحصائيات
pip install matplotlib pandas numpy seaborn

# مكتبات معالجة الصور
pip install opencv-python

# مكتبات الويب
pip install flask sqlalchemy

# مكتبات الذكاء الاصطناعي
pip install scikit-learn

# مكتبات الأمان
pip install bcrypt passlib cryptography
```

### 3. التشغيل

#### النسخة المتقدمة:
```bash
python run_advanced.py
```

#### النسخة الأساسية:
```bash
python main.py
```

#### النسخة المبسطة:
```bash
python run_simple.py
```

## ⚙️ الإعدادات المتقدمة

### 1. إعدادات الإشعارات
**الملف:** `config/notification_settings.json`

```json
{
  "enabled": true,
  "visual_enabled": true,
  "audio_enabled": true,
  "tts_enabled": true,
  "volume": 0.8,
  "reminder_times": [15, 10, 5, 1],
  "quiet_hours": {
    "enabled": true,
    "start": "22:00",
    "end": "06:00"
  }
}
```

### 2. إعدادات المحتوى
**الملف:** `config/content_settings.json`

```json
{
  "enabled": true,
  "auto_rotate": true,
  "rotation_interval": 15,
  "fade_transition": true,
  "max_file_size": 50,
  "supported_image_formats": [".jpg", ".jpeg", ".png", ".gif"],
  "supported_video_formats": [".mp4", ".avi", ".mov"]
}
```

### 3. إعدادات التحكم عن بُعد
**الملف:** `config/remote_settings.json`

```json
{
  "enabled": true,
  "port": 8080,
  "host": "0.0.0.0",
  "ssl_enabled": false,
  "require_authentication": true,
  "session_timeout": 3600
}
```

## 🎮 اختصارات لوحة المفاتيح

### الاختصارات الأساسية:
- **A**: فتح لوحة التحكم الإدارية
- **F11**: تبديل ملء الشاشة
- **Escape**: إغلاق النوافذ

### الاختصارات المتقدمة:
- **Ctrl+A**: إعدادات الإشعارات المتقدمة
- **Ctrl+C**: مدير المحتوى
- **Ctrl+S**: الإحصائيات والتقارير
- **Ctrl+R**: إعدادات التحكم عن بُعد
- **Ctrl+I**: معلومات النظام

## 🔧 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة:

#### 1. خطأ في تشغيل النسخة المتقدمة
```
ImportError: No module named 'matplotlib'
```
**الحل:**
```bash
pip install matplotlib pandas numpy
```

#### 2. خطأ في نظام الإشعارات
```
Error: TTS engine not available
```
**الحل:**
```bash
pip install pyttsx3
# أو على Linux:
sudo apt-get install espeak espeak-data libespeak-dev
```

#### 3. خطأ في التحكم عن بُعد
```
Error: Port 8080 already in use
```
**الحل:**
- غيّر المنفذ في `config/remote_settings.json`
- أو أوقف البرنامج الذي يستخدم المنفذ 8080

#### 4. خطأ في معالجة الصور
```
Error: OpenCV not found
```
**الحل:**
```bash
pip install opencv-python
```

## 📱 واجهة التحكم عن بُعد

### الوصول:
1. افتح المتصفح
2. اذهب إلى: `http://localhost:8080`
3. اسم المستخدم: `admin`
4. كلمة المرور: `admin123`

### الميزات المتاحة:
- **التحكم في الصوت**: تغيير مستوى الصوت، تشغيل/إيقاف الأذان
- **تحديث أوقات الصلاة**: تعديل الأوقات مباشرة
- **إرسال الإعلانات**: إضافة إعلانات فورية
- **مراقبة الحالة**: عرض حالة النظام والأداء

## 🔒 الأمان والخصوصية

### ميزات الأمان:
- **تشفير كلمات المرور**: باستخدام bcrypt
- **جلسات محدودة الوقت**: انتهاء صلاحية تلقائي
- **حماية من الهجمات**: Rate limiting وحماية CSRF
- **تسجيل الأنشطة**: مراقبة جميع العمليات

### أفضل الممارسات:
1. **غيّر كلمة المرور الافتراضية** فوراً
2. **استخدم HTTPS** في البيئات الإنتاجية
3. **قم بالنسخ الاحتياطي** للبيانات بانتظام
4. **راقب السجلات** للأنشطة المشبوهة

## 📈 الأداء والتحسين

### نصائح لتحسين الأداء:
1. **استخدم SSD** لتخزين البيانات
2. **أغلق البرامج غير الضرورية**
3. **راقب استخدام الذاكرة** من خلال نظام الإحصائيات
4. **نظف البيانات القديمة** بانتظام

### مراقبة الأداء:
- **استخدام المعالج**: يجب أن يكون أقل من 50%
- **استخدام الذاكرة**: يجب أن يكون أقل من 70%
- **وقت الاستجابة**: يجب أن يكون أقل من 2 ثانية

## 🆘 الدعم والمساعدة

### الحصول على المساعدة:
1. **راجع هذا الدليل** أولاً
2. **تحقق من ملف README_PYTHON.md**
3. **استخدم خاصية فحص النظام** في `run_advanced.py`
4. **راجع ملفات السجلات** في مجلد `logs/`

### الإبلاغ عن المشاكل:
- **وصف المشكلة** بالتفصيل
- **أرفق رسائل الخطأ** كاملة
- **اذكر نظام التشغيل** وإصدار Python
- **اذكر الخطوات** التي أدت للمشكلة

## 🔄 التحديثات المستقبلية

### ميزات مخطط لها:
- **الذكاء الاصطناعي**: تحليل أنماط الحضور
- **التكامل مع IoT**: ربط مع أنظمة الإضاءة والصوت
- **تطبيق موبايل**: تطبيق للهواتف الذكية
- **النسخ الاحتياطي السحابي**: حفظ البيانات في السحابة
- **دعم لغات متعددة**: ترجمة للغات أخرى

---

**جزاكم الله خيراً على استخدام هذا البرنامج في خدمة المجتمع الإسلامي** 🕌
