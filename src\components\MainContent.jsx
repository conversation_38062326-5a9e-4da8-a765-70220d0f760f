import React from 'react';
import TimeDisplay from './TimeDisplay';
import PrayerTimes from './PrayerTimes';
import CountdownCircle from './CountdownCircle';

const MainContent = ({ currentTime, prayerTimes }) => {
  return (
    <div className="main-content">
      <div className="left-section">
        <CountdownCircle 
          currentTime={currentTime}
          prayerTimes={prayerTimes}
        />
      </div>
      
      <div className="center-section">
        <TimeDisplay currentTime={currentTime} />
        <PrayerTimes prayerTimes={prayerTimes} />
      </div>
      
      <div className="right-section">
        <CountdownCircle 
          currentTime={currentTime}
          prayerTimes={prayerTimes}
          position="right"
        />
      </div>
    </div>
  );
};

export default MainContent;
