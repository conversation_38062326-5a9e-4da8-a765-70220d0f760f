#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للبرنامج
"""

import tkinter as tk
from datetime import datetime

def test_basic_app():
    """اختبار أساسي للتطبيق"""
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("اختبار شاشة أوقات الصلاة")
    root.geometry("800x600")
    root.configure(bg='#8B0000')
    
    # عنوان
    title_label = tk.Label(
        root,
        text="🕌 شاشة أوقات الصلاة للمسجد",
        font=('Arial', 24, 'bold'),
        bg='#8B0000',
        fg='white'
    )
    title_label.pack(pady=20)
    
    # الوقت الحالي
    time_label = tk.Label(
        root,
        text="",
        font=('Arial', 48, 'bold'),
        bg='#8B0000',
        fg='white'
    )
    time_label.pack(pady=20)
    
    # معلومات الحالة
    status_label = tk.Label(
        root,
        text="✅ البرنامج يعمل بنجاح!\n\nالميزات المتوفرة:\n• واجهة رسومية بالتصميم المطلوب\n• عرض الوقت الحالي\n• دعم اللغة العربية\n• تصميم أحمر أنيق",
        font=('Arial', 14),
        bg='#8B0000',
        fg='white',
        justify=tk.CENTER
    )
    status_label.pack(pady=20)
    
    # أوقات الصلاة التجريبية
    prayers_frame = tk.Frame(root, bg='#8B0000')
    prayers_frame.pack(pady=20)
    
    prayers = [
        ('الفجر', '4:11'),
        ('الظهر', '12:20'),
        ('العصر', '15:37'),
        ('المغرب', '19:01'),
        ('العشاء', '20:31')
    ]
    
    for i, (name, time) in enumerate(prayers):
        prayer_frame = tk.Frame(prayers_frame, bg='#B22222', relief=tk.RAISED, bd=2)
        prayer_frame.grid(row=0, column=i, padx=5, pady=5)
        
        name_label = tk.Label(prayer_frame, text=name, font=('Arial', 12, 'bold'), bg='#B22222', fg='white')
        name_label.pack(pady=5)
        
        time_label_prayer = tk.Label(prayer_frame, text=time, font=('Arial', 14), bg='#B22222', fg='white')
        time_label_prayer.pack(pady=5)
    
    # تعليمات
    instructions_label = tk.Label(
        root,
        text="اضغط Escape للخروج • F11 لملء الشاشة",
        font=('Arial', 10),
        bg='#8B0000',
        fg='white'
    )
    instructions_label.pack(side=tk.BOTTOM, pady=10)
    
    def update_time():
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%H:%M:%S")
        time_label.config(text=current_time)
        root.after(1000, update_time)
    
    def toggle_fullscreen(event=None):
        """تبديل ملء الشاشة"""
        current_state = root.attributes('-fullscreen')
        root.attributes('-fullscreen', not current_state)
    
    def quit_app(event=None):
        """إنهاء التطبيق"""
        root.quit()
    
    # ربط الأحداث
    root.bind('<Escape>', quit_app)
    root.bind('<F11>', toggle_fullscreen)
    root.focus_set()
    
    # بدء تحديث الوقت
    update_time()
    
    # تشغيل التطبيق
    root.mainloop()

if __name__ == "__main__":
    print("🚀 بدء اختبار البرنامج...")
    print("✅ Python يعمل بنجاح")
    print("✅ tkinter متوفر")
    print("🕌 تشغيل واجهة المسجد...")
    
    try:
        test_basic_app()
        print("✅ تم إنهاء البرنامج بنجاح")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
