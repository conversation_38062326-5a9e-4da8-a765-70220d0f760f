import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import MainContent from './components/MainContent';
import Footer from './components/Footer';
import AdminPanel from './components/AdminPanel';
import { getPrayerTimes } from './services/prayerTimesService';
import { getWeatherData } from './services/weatherService';
import azanService from './services/azanService';
import './App.css';

function App() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [prayerTimes, setPrayerTimes] = useState(null);
  const [weatherData, setWeatherData] = useState(null);
  const [location, setLocation] = useState({ latitude: 36.7538, longitude: 3.0588 }); // Default: Algiers
  const [currentText, setCurrentText] = useState(0);
  const [showAdminPanel, setShowAdminPanel] = useState(false);

  // Islamic texts to display
  const islamicTexts = [
    "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
    "إِنَّ مَعَ الْعُسْرِ يُسْرًا",
    "وَاللَّهُ خَيْرٌ حَافِظًا وَهُوَ أَرْحَمُ الرَّاحِمِينَ",
    "قال رسول الله ﷺ: الصلاة نور",
    "قال رسول الله ﷺ: خير الناس أنفعهم للناس",
    "وَبَشِّرِ الصَّابِرِينَ",
    "رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً"
  ];

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Get user location
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        (error) => {
          console.log('Location access denied, using default location');
        }
      );
    }
  }, []);

  // Fetch prayer times
  useEffect(() => {
    const fetchPrayerTimes = async () => {
      try {
        const times = await getPrayerTimes(location.latitude, location.longitude);
        setPrayerTimes(times);

        // Schedule azan for prayer times
        azanService.scheduleAzan(times);
      } catch (error) {
        console.error('Error fetching prayer times:', error);
      }
    };

    fetchPrayerTimes();
    // Refresh prayer times every hour
    const interval = setInterval(fetchPrayerTimes, 3600000);
    return () => clearInterval(interval);
  }, [location]);

  // Fetch weather data
  useEffect(() => {
    const fetchWeather = async () => {
      try {
        const weather = await getWeatherData(location.latitude, location.longitude);
        setWeatherData(weather);
      } catch (error) {
        console.error('Error fetching weather:', error);
      }
    };

    fetchWeather();
    // Refresh weather every 30 minutes
    const interval = setInterval(fetchWeather, 1800000);
    return () => clearInterval(interval);
  }, [location]);

  // Rotate Islamic texts every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentText((prev) => (prev + 1) % islamicTexts.length);
    }, 10000);

    return () => clearInterval(interval);
  }, [islamicTexts.length]);

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initAudio = async () => {
      await azanService.initializeAudio();
    };

    const handleUserInteraction = () => {
      initAudio();
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
    };

    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);

    return () => {
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
    };
  }, []);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event) => {
      // Press 'A' key to open admin panel
      if (event.key === 'a' || event.key === 'A') {
        setShowAdminPanel(true);
      }
      // Press 'Escape' to close admin panel
      if (event.key === 'Escape') {
        setShowAdminPanel(false);
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, []);

  const handleUpdatePrayerTimes = (newTimes) => {
    setPrayerTimes(newTimes);
  };

  return (
    <div className="app">
      <div className="decorative-pattern"></div>
      <div className="islamic-pattern"></div>
      
      <div className="main-container">
        <Header 
          weatherData={weatherData}
          currentTime={currentTime}
          location={location}
        />
        
        <MainContent 
          currentTime={currentTime}
          prayerTimes={prayerTimes}
        />
        
        <Footer
          islamicText={islamicTexts[currentText]}
        />
      </div>

      <AdminPanel
        isOpen={showAdminPanel}
        onClose={() => setShowAdminPanel(false)}
        prayerTimes={prayerTimes}
        onUpdatePrayerTimes={handleUpdatePrayerTimes}
      />
    </div>
  );
}

export default App;
