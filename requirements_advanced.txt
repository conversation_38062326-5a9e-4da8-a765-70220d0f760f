# متطلبات المشروع - شاشة أوقات الصلاة المتقدمة
# Project Requirements - Advanced Mosque Prayer Times Display

# ===== المكتبات الأساسية - Core Libraries =====
tkinter
requests>=2.31.0
pygame>=2.5.0
Pillow>=10.0.0
python-bidi>=0.4.2
arabic-reshaper>=3.0.0
hijri-converter>=2.3.1
geopy>=2.3.0
pyttsx3>=2.90
schedule>=1.2.0
configparser>=6.0.0
pyinstaller>=5.13.0

# ===== الميزات المتقدمة - Advanced Features =====

# مكتبات الرسوم البيانية والإحصائيات - Charts and Analytics
matplotlib>=3.7.0
pandas>=2.0.0
numpy>=1.24.0
seaborn>=0.12.0

# مكتبات الذكاء الاصطناعي - AI Libraries
scikit-learn>=1.3.0
# tensorflow>=2.13.0  # اختياري - Optional
# torch>=2.0.0  # اختياري - Optional

# مكتبات معالجة الصور والفيديو - Image and Video Processing
opencv-python>=4.8.0
imageio>=2.31.0
# moviepy>=1.0.3  # اختياري - Optional

# مكتبات الويب والخوادم - Web and Server Libraries
flask>=2.3.0
# fastapi>=0.100.0  # اختياري - Optional
# uvicorn>=0.23.0  # اختياري - Optional

# مكتبات قواعد البيانات المتقدمة - Advanced Database Libraries
sqlalchemy>=2.0.0
# pymongo>=4.4.0  # اختياري - Optional

# مكتبات التحليل والمراقبة - Analytics and Monitoring
# prometheus-client>=0.17.0  # اختياري - Optional
# grafana-api>=1.0.3  # اختياري - Optional

# مكتبات الأمان المتقدمة - Advanced Security
bcrypt>=4.0.0
# jwt>=1.3.1
passlib>=1.7.4
cryptography>=41.0.0

# مكتبات التخزين السحابي - Cloud Storage
# boto3>=1.28.0  # اختياري - Optional
# google-cloud-storage>=2.10.0  # اختياري - Optional

# مكتبات الترجمة - Translation Libraries
# googletrans>=4.0.0
# deep-translator>=1.11.0

# مكتبات الإشعارات المتقدمة - Advanced Notifications
plyer>=2.1.0
# win10toast>=0.9  # Windows only
# pync>=2.0.3  # macOS only

# مكتبات التحكم في الأجهزة - Hardware Control
# pyserial>=3.5
# RPi.GPIO>=0.7.1  # Raspberry Pi only

# مكتبات الشبكات المتقدمة - Advanced Networking
# websockets>=11.0
# socketio>=5.8.0
# mqtt>=1.6.1  # اختياري - Optional

# مكتبات التحليل النصي - Text Analysis
# nltk>=3.8
# textblob>=0.17.1

# مكتبات التصدير والتقارير - Export and Reporting
# reportlab>=4.0.0
# openpyxl>=3.1.0
# xlsxwriter>=3.1.0

# مكتبات الضغط والأرشفة - Compression and Archiving
# py7zr>=0.20.0
# rarfile>=4.0

# مكتبات التحقق والاختبار - Validation and Testing
# pytest>=7.4.0
# pytest-cov>=4.1.0
# black>=23.7.0
# flake8>=6.0.0

# مكتبات التوثيق - Documentation
# sphinx>=7.1.0
# mkdocs>=1.5.0

# مكتبات الأداء - Performance
# cython>=3.0.0
# numba>=0.57.0

# مكتبات التحكم في العمليات المتقدمة - Advanced Process Control
# celery>=5.3.0  # اختياري - Optional
# redis>=4.6.0  # اختياري - Optional

# مكتبات التحكم في النوافذ - Window Management
# pygetwindow>=0.0.9
# pyautogui>=0.9.54

# مكتبات الصوت المتقدمة - Advanced Audio
# pydub>=0.25.1
# librosa>=0.10.0
# soundfile>=0.12.1

# مكتبات التحكم في الإضاءة - Lighting Control
# phue>=1.1  # Philips Hue
# lifxlan>=1.2.7  # LIFX

# مكتبات إنترنت الأشياء - IoT Libraries
# paho-mqtt>=1.6.1
# bleak>=0.20.0  # Bluetooth

# مكتبات التحكم في الكاميرات - Camera Control
# opencv-contrib-python>=4.8.0
# picamera>=1.13  # Raspberry Pi Camera

# مكتبات النسخ الاحتياطي - Backup Libraries
# duplicity>=0.8.23  # اختياري - Optional
# rclone>=1.63.0  # اختياري - Optional

# مكتبات النظام - System Libraries
psutil>=5.9.0
python-dateutil>=2.8.2
pytz>=2023.3

# مكتبات الشبكة - Network Libraries
urllib3>=2.0.0

# مكتبات الصوت - Audio Libraries
playsound>=1.3.0

# ===== ملاحظات التثبيت - Installation Notes =====
#
# للتثبيت الأساسي (الميزات الأساسية فقط):
# pip install -r requirements.txt
#
# للتثبيت المتقدم (جميع الميزات):
# pip install -r requirements_advanced.txt
#
# للتثبيت التدريجي:
# 1. pip install -r requirements.txt
# 2. pip install matplotlib pandas numpy seaborn
# 3. pip install opencv-python flask sqlalchemy
# 4. pip install scikit-learn bcrypt passlib
#
# ملاحظة: بعض المكتبات قد تحتاج إعدادات إضافية
# أو قد لا تكون متوفرة على جميع أنظمة التشغيل
#
# للحصول على أفضل أداء، استخدم بيئة افتراضية:
# python -m venv mosque_env
# mosque_env\Scripts\activate  # Windows
# source mosque_env/bin/activate  # Linux/Mac
# pip install -r requirements_advanced.txt
