#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الإحصائيات والتقارير المتقدم
Advanced Analytics and Reporting System
"""

import tkinter as tk
from tkinter import ttk
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
import threading
import time
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import numpy as np

class MetricType(Enum):
    """أنواع المقاييس"""
    SYSTEM_UPTIME = "system_uptime"
    PRAYER_ATTENDANCE = "prayer_attendance"
    CONTENT_VIEWS = "content_views"
    NOTIFICATION_SENT = "notification_sent"
    USER_INTERACTION = "user_interaction"
    SYSTEM_PERFORMANCE = "system_performance"
    ERROR_COUNT = "error_count"
    WEATHER_UPDATES = "weather_updates"
    PRAYER_TIME_UPDATES = "prayer_time_updates"

class ReportType(Enum):
    """أنواع التقارير"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"
    CUSTOM = "custom"

class AnalyticsEvent:
    """حدث إحصائي"""
    
    def __init__(self,
                 event_id: str,
                 metric_type: MetricType,
                 value: Any,
                 timestamp: datetime = None,
                 metadata: Dict = None):
        
        self.id = event_id
        self.metric_type = metric_type
        self.value = value
        self.timestamp = timestamp or datetime.now()
        self.metadata = metadata or {}

class AdvancedAnalyticsSystem:
    """نظام الإحصائيات والتقارير المتقدم"""
    
    def __init__(self, parent_window):
        self.parent = parent_window
        self.db_path = "data/analytics.db"
        self.connection = None
        
        # إعدادات النظام
        self.settings = self.load_settings()
        
        # مكونات الواجهة
        self.analytics_window = None
        self.charts = {}
        
        # خيوط العمل
        self.analytics_thread = None
        self.is_running = True
        
        # بيانات الإحصائيات
        self.current_metrics = {}
        self.performance_data = []
        
        self.initialize_analytics_system()
        self.start_analytics_collection()
    
    def load_settings(self) -> Dict:
        """تحميل إعدادات الإحصائيات"""
        default_settings = {
            "enabled": True,
            "collection_interval": 60,  # ثانية
            "retention_days": 365,  # أيام
            "auto_cleanup": True,
            "real_time_monitoring": True,
            "performance_tracking": True,
            "error_tracking": True,
            "user_analytics": True,
            "export_enabled": True,
            "chart_refresh_interval": 300,  # ثانية
            "max_data_points": 1000,
            "alert_thresholds": {
                "cpu_usage": 80,
                "memory_usage": 85,
                "error_rate": 5,
                "response_time": 2000
            }
        }
        
        try:
            with open('config/analytics_settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
        except:
            return default_settings
    
    def save_settings(self):
        """حفظ إعدادات الإحصائيات"""
        try:
            with open('config/analytics_settings.json', 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الإحصائيات: {e}")
    
    def initialize_analytics_system(self):
        """تهيئة نظام الإحصائيات"""
        try:
            # إنشاء مجلد البيانات
            import os
            os.makedirs('data', exist_ok=True)
            
            # إنشاء قاعدة البيانات
            self.setup_database()
            
            print("✅ تم تهيئة نظام الإحصائيات والتقارير")
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة نظام الإحصائيات: {e}")
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            cursor = self.connection.cursor()
            
            # جدول الأحداث الإحصائية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analytics_events (
                    id TEXT PRIMARY KEY,
                    metric_type TEXT NOT NULL,
                    value TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    metadata TEXT
                )
            ''')
            
            # جدول الأداء
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    cpu_usage REAL,
                    memory_usage REAL,
                    disk_usage REAL,
                    response_time REAL,
                    active_users INTEGER,
                    error_count INTEGER
                )
            ''')
            
            # جدول الحضور
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS attendance_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    prayer_name TEXT NOT NULL,
                    date DATE NOT NULL,
                    estimated_attendance INTEGER,
                    actual_attendance INTEGER,
                    weather_condition TEXT,
                    special_event BOOLEAN DEFAULT 0
                )
            ''')
            
            # جدول التقارير
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reports (
                    id TEXT PRIMARY KEY,
                    report_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    created_at DATETIME NOT NULL,
                    parameters TEXT
                )
            ''')
            
            # إنشاء فهارس للأداء
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_timestamp ON analytics_events(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_metric_type ON analytics_events(metric_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_performance_timestamp ON performance_metrics(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance_data(date)')
            
            self.connection.commit()
            print("✅ تم إعداد قاعدة بيانات الإحصائيات")
            
        except Exception as e:
            print(f"خطأ في إعداد قاعدة البيانات: {e}")
    
    def start_analytics_collection(self):
        """بدء جمع الإحصائيات"""
        self.analytics_thread = threading.Thread(
            target=self.analytics_collection_loop,
            daemon=True
        )
        self.analytics_thread.start()
    
    def analytics_collection_loop(self):
        """حلقة جمع الإحصائيات"""
        while self.is_running:
            try:
                if self.settings.get("enabled", True):
                    self.collect_system_metrics()
                    self.collect_performance_metrics()
                    self.cleanup_old_data()
                
                time.sleep(self.settings.get("collection_interval", 60))
                
            except Exception as e:
                print(f"خطأ في جمع الإحصائيات: {e}")
                time.sleep(60)
    
    def collect_system_metrics(self):
        """جمع مقاييس النظام"""
        try:
            import psutil
            
            # استخدام المعالج
            cpu_usage = psutil.cpu_percent(interval=1)
            
            # استخدام الذاكرة
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # استخدام القرص
            disk = psutil.disk_usage('/')
            disk_usage = disk.percent
            
            # حفظ البيانات
            self.record_event(
                f"system_metrics_{int(time.time())}",
                MetricType.SYSTEM_PERFORMANCE,
                {
                    "cpu_usage": cpu_usage,
                    "memory_usage": memory_usage,
                    "disk_usage": disk_usage
                }
            )
            
            # تحديث المقاييس الحالية
            self.current_metrics.update({
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "disk_usage": disk_usage
            })
            
            # فحص التحذيرات
            self.check_performance_alerts(cpu_usage, memory_usage)
            
        except ImportError:
            # psutil غير متوفر، استخدم بيانات وهمية
            self.current_metrics.update({
                "cpu_usage": np.random.uniform(10, 30),
                "memory_usage": np.random.uniform(40, 60),
                "disk_usage": np.random.uniform(20, 40)
            })
        except Exception as e:
            print(f"خطأ في جمع مقاييس النظام: {e}")
    
    def collect_performance_metrics(self):
        """جمع مقاييس الأداء"""
        try:
            cursor = self.connection.cursor()
            
            # حساب وقت الاستجابة (محاكاة)
            response_time = np.random.uniform(100, 500)  # ميلي ثانية
            
            # عدد المستخدمين النشطين (محاكاة)
            active_users = np.random.randint(1, 10)
            
            # عدد الأخطاء
            error_count = self.get_recent_error_count()
            
            # إدراج البيانات
            cursor.execute('''
                INSERT INTO performance_metrics 
                (timestamp, cpu_usage, memory_usage, disk_usage, response_time, active_users, error_count)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now(),
                self.current_metrics.get("cpu_usage", 0),
                self.current_metrics.get("memory_usage", 0),
                self.current_metrics.get("disk_usage", 0),
                response_time,
                active_users,
                error_count
            ))
            
            self.connection.commit()
            
        except Exception as e:
            print(f"خطأ في جمع مقاييس الأداء: {e}")
    
    def record_event(self, event_id: str, metric_type: MetricType, value: Any, metadata: Dict = None):
        """تسجيل حدث إحصائي"""
        try:
            if not self.settings.get("enabled", True):
                return
            
            cursor = self.connection.cursor()
            
            # تحويل القيمة إلى JSON إذا كانت معقدة
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value, ensure_ascii=False)
            else:
                value_str = str(value)
            
            # تحويل البيانات الوصفية إلى JSON
            metadata_str = json.dumps(metadata or {}, ensure_ascii=False)
            
            cursor.execute('''
                INSERT INTO analytics_events (id, metric_type, value, timestamp, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (event_id, metric_type.value, value_str, datetime.now(), metadata_str))
            
            self.connection.commit()
            
        except Exception as e:
            print(f"خطأ في تسجيل الحدث الإحصائي: {e}")
    
    def get_recent_error_count(self) -> int:
        """الحصول على عدد الأخطاء الأخيرة"""
        try:
            # محاكاة عدد الأخطاء
            return np.random.randint(0, 3)
        except:
            return 0
    
    def check_performance_alerts(self, cpu_usage: float, memory_usage: float):
        """فحص تحذيرات الأداء"""
        try:
            thresholds = self.settings.get("alert_thresholds", {})
            
            if cpu_usage > thresholds.get("cpu_usage", 80):
                self.send_performance_alert("تحذير: استخدام المعالج مرتفع", f"استخدام المعالج: {cpu_usage:.1f}%")
            
            if memory_usage > thresholds.get("memory_usage", 85):
                self.send_performance_alert("تحذير: استخدام الذاكرة مرتفع", f"استخدام الذاكرة: {memory_usage:.1f}%")
                
        except Exception as e:
            print(f"خطأ في فحص تحذيرات الأداء: {e}")
    
    def send_performance_alert(self, title: str, message: str):
        """إرسال تحذير الأداء"""
        try:
            print(f"⚠️ {title}: {message}")
            
            # يمكن ربطها مع نظام الإشعارات
            # self.notification_system.add_notification(...)
            
        except Exception as e:
            print(f"خطأ في إرسال تحذير الأداء: {e}")
    
    def generate_daily_report(self, date: datetime = None) -> Dict:
        """إنشاء تقرير يومي"""
        try:
            if date is None:
                date = datetime.now().date()
            
            cursor = self.connection.cursor()
            
            # إحصائيات الأداء
            cursor.execute('''
                SELECT AVG(cpu_usage), AVG(memory_usage), AVG(response_time), 
                       SUM(error_count), MAX(active_users)
                FROM performance_metrics 
                WHERE DATE(timestamp) = ?
            ''', (date,))
            
            perf_data = cursor.fetchone()
            
            # إحصائيات الأحداث
            cursor.execute('''
                SELECT metric_type, COUNT(*) 
                FROM analytics_events 
                WHERE DATE(timestamp) = ?
                GROUP BY metric_type
            ''', (date,))
            
            events_data = cursor.fetchall()
            
            # تكوين التقرير
            report = {
                "date": date.isoformat(),
                "performance": {
                    "avg_cpu_usage": round(perf_data[0] or 0, 2),
                    "avg_memory_usage": round(perf_data[1] or 0, 2),
                    "avg_response_time": round(perf_data[2] or 0, 2),
                    "total_errors": perf_data[3] or 0,
                    "max_active_users": perf_data[4] or 0
                },
                "events": {event_type: count for event_type, count in events_data},
                "generated_at": datetime.now().isoformat()
            }
            
            # حفظ التقرير
            self.save_report(f"daily_{date.isoformat()}", ReportType.DAILY, "تقرير يومي", report)
            
            return report
            
        except Exception as e:
            print(f"خطأ في إنشاء التقرير اليومي: {e}")
            return {}
    
    def generate_weekly_report(self, start_date: datetime = None) -> Dict:
        """إنشاء تقرير أسبوعي"""
        try:
            if start_date is None:
                start_date = datetime.now().date() - timedelta(days=7)
            
            end_date = start_date + timedelta(days=7)
            
            cursor = self.connection.cursor()
            
            # إحصائيات الأسبوع
            cursor.execute('''
                SELECT DATE(timestamp), AVG(cpu_usage), AVG(memory_usage), 
                       SUM(error_count), AVG(active_users)
                FROM performance_metrics 
                WHERE DATE(timestamp) BETWEEN ? AND ?
                GROUP BY DATE(timestamp)
                ORDER BY DATE(timestamp)
            ''', (start_date, end_date))
            
            daily_data = cursor.fetchall()
            
            # تحليل الاتجاهات
            cpu_trend = self.calculate_trend([row[1] for row in daily_data if row[1]])
            memory_trend = self.calculate_trend([row[2] for row in daily_data if row[2]])
            
            report = {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "daily_performance": [
                    {
                        "date": row[0],
                        "avg_cpu": round(row[1] or 0, 2),
                        "avg_memory": round(row[2] or 0, 2),
                        "errors": row[3] or 0,
                        "avg_users": round(row[4] or 0, 2)
                    } for row in daily_data
                ],
                "trends": {
                    "cpu_trend": cpu_trend,
                    "memory_trend": memory_trend
                },
                "generated_at": datetime.now().isoformat()
            }
            
            # حفظ التقرير
            self.save_report(f"weekly_{start_date.isoformat()}", ReportType.WEEKLY, "تقرير أسبوعي", report)
            
            return report
            
        except Exception as e:
            print(f"خطأ في إنشاء التقرير الأسبوعي: {e}")
            return {}
    
    def calculate_trend(self, values: List[float]) -> str:
        """حساب اتجاه البيانات"""
        try:
            if len(values) < 2:
                return "غير محدد"
            
            # حساب الانحدار الخطي البسيط
            x = np.arange(len(values))
            slope = np.polyfit(x, values, 1)[0]
            
            if slope > 0.1:
                return "متزايد"
            elif slope < -0.1:
                return "متناقص"
            else:
                return "مستقر"
                
        except Exception as e:
            print(f"خطأ في حساب الاتجاه: {e}")
            return "غير محدد"
    
    def save_report(self, report_id: str, report_type: ReportType, title: str, content: Dict):
        """حفظ التقرير"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO reports (id, report_type, title, content, created_at, parameters)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                report_id,
                report_type.value,
                title,
                json.dumps(content, ensure_ascii=False),
                datetime.now(),
                json.dumps({}, ensure_ascii=False)
            ))
            
            self.connection.commit()
            
        except Exception as e:
            print(f"خطأ في حفظ التقرير: {e}")
    
    def get_performance_chart_data(self, days: int = 7) -> Dict:
        """الحصول على بيانات الرسم البياني للأداء"""
        try:
            cursor = self.connection.cursor()
            
            start_date = datetime.now() - timedelta(days=days)
            
            cursor.execute('''
                SELECT timestamp, cpu_usage, memory_usage, response_time
                FROM performance_metrics 
                WHERE timestamp >= ?
                ORDER BY timestamp
            ''', (start_date,))
            
            data = cursor.fetchall()
            
            if not data:
                return {}
            
            timestamps = [datetime.fromisoformat(row[0]) for row in data]
            cpu_data = [row[1] for row in data]
            memory_data = [row[2] for row in data]
            response_data = [row[3] for row in data]
            
            return {
                "timestamps": timestamps,
                "cpu_usage": cpu_data,
                "memory_usage": memory_data,
                "response_time": response_data
            }
            
        except Exception as e:
            print(f"خطأ في الحصول على بيانات الرسم البياني: {e}")
            return {}
    
    def create_performance_chart(self, parent_widget, days: int = 7):
        """إنشاء رسم بياني للأداء"""
        try:
            data = self.get_performance_chart_data(days)
            
            if not data:
                return None
            
            # إنشاء الرسم البياني
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 6))
            fig.patch.set_facecolor('#2d2d2d')
            
            # رسم استخدام المعالج والذاكرة
            ax1.plot(data["timestamps"], data["cpu_usage"], label="استخدام المعالج", color='#ff6b6b')
            ax1.plot(data["timestamps"], data["memory_usage"], label="استخدام الذاكرة", color='#4ecdc4')
            ax1.set_ylabel("النسبة المئوية")
            ax1.set_title("أداء النظام")
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            ax1.set_facecolor('#3d3d3d')
            
            # رسم وقت الاستجابة
            ax2.plot(data["timestamps"], data["response_time"], label="وقت الاستجابة", color='#45b7d1')
            ax2.set_ylabel("ميلي ثانية")
            ax2.set_xlabel("الوقت")
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            ax2.set_facecolor('#3d3d3d')
            
            # تنسيق التواريخ
            for ax in [ax1, ax2]:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            plt.tight_layout()
            
            # إنشاء Canvas
            canvas = FigureCanvasTkAgg(fig, parent_widget)
            canvas.draw()
            
            return canvas.get_tk_widget()
            
        except Exception as e:
            print(f"خطأ في إنشاء الرسم البياني: {e}")
            return None
    
    def cleanup_old_data(self):
        """تنظيف البيانات القديمة"""
        try:
            if not self.settings.get("auto_cleanup", True):
                return
            
            retention_days = self.settings.get("retention_days", 365)
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            cursor = self.connection.cursor()
            
            # حذف الأحداث القديمة
            cursor.execute('DELETE FROM analytics_events WHERE timestamp < ?', (cutoff_date,))
            
            # حذف مقاييس الأداء القديمة
            cursor.execute('DELETE FROM performance_metrics WHERE timestamp < ?', (cutoff_date,))
            
            # حذف التقارير القديمة
            cursor.execute('DELETE FROM reports WHERE created_at < ?', (cutoff_date,))
            
            self.connection.commit()
            
            deleted_count = cursor.rowcount
            if deleted_count > 0:
                print(f"🧹 تم حذف {deleted_count} سجل قديم")
                
        except Exception as e:
            print(f"خطأ في تنظيف البيانات القديمة: {e}")
    
    def export_data(self, start_date: datetime, end_date: datetime, file_path: str):
        """تصدير البيانات"""
        try:
            cursor = self.connection.cursor()
            
            # جلب البيانات
            cursor.execute('''
                SELECT * FROM performance_metrics 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp
            ''', (start_date, end_date))
            
            data = cursor.fetchall()
            
            # تحويل إلى DataFrame
            df = pd.DataFrame(data, columns=[
                'id', 'timestamp', 'cpu_usage', 'memory_usage', 
                'disk_usage', 'response_time', 'active_users', 'error_count'
            ])
            
            # تصدير إلى CSV
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            print(f"✅ تم تصدير البيانات إلى {file_path}")
            
        except Exception as e:
            print(f"خطأ في تصدير البيانات: {e}")
    
    def get_system_summary(self) -> Dict:
        """الحصول على ملخص النظام"""
        try:
            cursor = self.connection.cursor()
            
            # إحصائيات اليوم
            today = datetime.now().date()
            cursor.execute('''
                SELECT AVG(cpu_usage), AVG(memory_usage), SUM(error_count)
                FROM performance_metrics 
                WHERE DATE(timestamp) = ?
            ''', (today,))
            
            today_stats = cursor.fetchone()
            
            # إجمالي الأحداث
            cursor.execute('SELECT COUNT(*) FROM analytics_events')
            total_events = cursor.fetchone()[0]
            
            # وقت التشغيل (محاكاة)
            uptime_hours = np.random.randint(24, 720)  # من يوم إلى شهر
            
            return {
                "uptime_hours": uptime_hours,
                "total_events": total_events,
                "today_avg_cpu": round(today_stats[0] or 0, 1),
                "today_avg_memory": round(today_stats[1] or 0, 1),
                "today_errors": today_stats[2] or 0,
                "current_cpu": round(self.current_metrics.get("cpu_usage", 0), 1),
                "current_memory": round(self.current_metrics.get("memory_usage", 0), 1),
                "status": "نشط" if self.is_running else "متوقف"
            }
            
        except Exception as e:
            print(f"خطأ في الحصول على ملخص النظام: {e}")
            return {}
    
    def stop(self):
        """إيقاف نظام الإحصائيات"""
        self.is_running = False
        
        if self.connection:
            self.connection.close()
        
        print("🛑 تم إيقاف نظام الإحصائيات والتقارير")
