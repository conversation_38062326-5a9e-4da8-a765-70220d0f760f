#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المحتوى الديناميكي
Dynamic Content Management System
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from PIL import Image, ImageTk
import threading
import time
from typing import Dict, List, Optional, Any
from enum import Enum
import cv2

class ContentType(Enum):
    """أنواع المحتوى"""
    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    ANNOUNCEMENT = "announcement"
    HADITH = "hadith"
    QURAN = "quran"
    EVENT = "event"
    WEATHER = "weather"

class ContentPriority(Enum):
    """أولوية المحتوى"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

class ContentStatus(Enum):
    """حالة المحتوى"""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    ACTIVE = "active"
    EXPIRED = "expired"
    PAUSED = "paused"

class ContentItem:
    """عنصر المحتوى"""
    
    def __init__(self, 
                 content_id: str,
                 title: str,
                 content_type: ContentType,
                 data: Any,
                 priority: ContentPriority = ContentPriority.NORMAL,
                 duration: int = 10,
                 start_time: Optional[datetime] = None,
                 end_time: Optional[datetime] = None,
                 repeat_schedule: Optional[Dict] = None,
                 tags: List[str] = None,
                 metadata: Dict = None):
        
        self.id = content_id
        self.title = title
        self.type = content_type
        self.data = data
        self.priority = priority
        self.duration = duration
        self.start_time = start_time
        self.end_time = end_time
        self.repeat_schedule = repeat_schedule or {}
        self.tags = tags or []
        self.metadata = metadata or {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.view_count = 0
        self.status = ContentStatus.DRAFT

class DynamicContentManager:
    """مدير المحتوى الديناميكي"""
    
    def __init__(self, parent_window):
        self.parent = parent_window
        self.content_items = {}
        self.content_queue = []
        self.current_content = None
        self.display_area = None
        
        # إعدادات النظام
        self.settings = self.load_settings()
        self.content_dir = Path("content")
        self.content_dir.mkdir(exist_ok=True)
        
        # مكونات العرض
        self.image_cache = {}
        self.video_player = None
        
        # خيوط العمل
        self.content_thread = None
        self.is_running = True
        
        self.initialize_content_system()
        self.load_content_items()
        self.start_content_scheduler()
    
    def load_settings(self) -> Dict:
        """تحميل إعدادات المحتوى"""
        default_settings = {
            "enabled": True,
            "auto_rotate": True,
            "rotation_interval": 15,  # ثواني
            "fade_transition": True,
            "transition_duration": 1,  # ثواني
            "max_image_size": (1920, 1080),
            "supported_image_formats": [".jpg", ".jpeg", ".png", ".gif", ".bmp"],
            "supported_video_formats": [".mp4", ".avi", ".mov", ".mkv"],
            "max_file_size": 50,  # ميجابايت
            "content_areas": {
                "main": {"x": 0, "y": 0, "width": 1920, "height": 1080},
                "sidebar": {"x": 1600, "y": 100, "width": 300, "height": 800},
                "footer": {"x": 0, "y": 900, "width": 1920, "height": 180}
            }
        }
        
        try:
            with open('config/content_settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
        except:
            return default_settings
    
    def save_settings(self):
        """حفظ إعدادات المحتوى"""
        try:
            with open('config/content_settings.json', 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات المحتوى: {e}")
    
    def initialize_content_system(self):
        """تهيئة نظام المحتوى"""
        try:
            # إنشاء مجلدات المحتوى
            (self.content_dir / "images").mkdir(exist_ok=True)
            (self.content_dir / "videos").mkdir(exist_ok=True)
            (self.content_dir / "announcements").mkdir(exist_ok=True)
            (self.content_dir / "templates").mkdir(exist_ok=True)
            
            print("✅ تم تهيئة نظام إدارة المحتوى")
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة نظام المحتوى: {e}")
    
    def load_content_items(self):
        """تحميل عناصر المحتوى"""
        try:
            content_file = self.content_dir / "content_items.json"
            if content_file.exists():
                with open(content_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for item_data in data:
                    content_item = self.dict_to_content_item(item_data)
                    self.content_items[content_item.id] = content_item
            
            print(f"✅ تم تحميل {len(self.content_items)} عنصر محتوى")
            
        except Exception as e:
            print(f"خطأ في تحميل المحتوى: {e}")
    
    def save_content_items(self):
        """حفظ عناصر المحتوى"""
        try:
            content_file = self.content_dir / "content_items.json"
            data = []
            
            for content_item in self.content_items.values():
                data.append(self.content_item_to_dict(content_item))
            
            with open(content_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            print("✅ تم حفظ عناصر المحتوى")
            
        except Exception as e:
            print(f"خطأ في حفظ المحتوى: {e}")
    
    def add_content_item(self, content_item: ContentItem) -> bool:
        """إضافة عنصر محتوى جديد"""
        try:
            # التحقق من صحة المحتوى
            if not self.validate_content_item(content_item):
                return False
            
            # إضافة للقاموس
            self.content_items[content_item.id] = content_item
            
            # حفظ التغييرات
            self.save_content_items()
            
            # تحديث جدولة العرض
            self.update_content_queue()
            
            print(f"✅ تم إضافة محتوى: {content_item.title}")
            return True
            
        except Exception as e:
            print(f"خطأ في إضافة المحتوى: {e}")
            return False
    
    def validate_content_item(self, content_item: ContentItem) -> bool:
        """التحقق من صحة عنصر المحتوى"""
        try:
            # التحقق من البيانات الأساسية
            if not content_item.title or not content_item.data:
                return False
            
            # التحقق من نوع المحتوى
            if content_item.type == ContentType.IMAGE:
                return self.validate_image_content(content_item.data)
            elif content_item.type == ContentType.VIDEO:
                return self.validate_video_content(content_item.data)
            elif content_item.type == ContentType.TEXT:
                return self.validate_text_content(content_item.data)
            
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من المحتوى: {e}")
            return False
    
    def validate_image_content(self, image_path: str) -> bool:
        """التحقق من صحة الصورة"""
        try:
            if not os.path.exists(image_path):
                return False
            
            # التحقق من التنسيق
            file_ext = Path(image_path).suffix.lower()
            if file_ext not in self.settings["supported_image_formats"]:
                return False
            
            # التحقق من حجم الملف
            file_size = os.path.getsize(image_path) / (1024 * 1024)  # ميجابايت
            if file_size > self.settings["max_file_size"]:
                return False
            
            # التحقق من إمكانية فتح الصورة
            with Image.open(image_path) as img:
                img.verify()
            
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من الصورة: {e}")
            return False
    
    def validate_video_content(self, video_path: str) -> bool:
        """التحقق من صحة الفيديو"""
        try:
            if not os.path.exists(video_path):
                return False
            
            # التحقق من التنسيق
            file_ext = Path(video_path).suffix.lower()
            if file_ext not in self.settings["supported_video_formats"]:
                return False
            
            # التحقق من حجم الملف
            file_size = os.path.getsize(video_path) / (1024 * 1024)  # ميجابايت
            if file_size > self.settings["max_file_size"]:
                return False
            
            # التحقق من إمكانية فتح الفيديو
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return False
            cap.release()
            
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من الفيديو: {e}")
            return False
    
    def validate_text_content(self, text_data: Dict) -> bool:
        """التحقق من صحة المحتوى النصي"""
        try:
            required_fields = ["text", "font_size", "color"]
            return all(field in text_data for field in required_fields)
            
        except Exception as e:
            print(f"خطأ في التحقق من النص: {e}")
            return False
    
    def start_content_scheduler(self):
        """بدء جدولة المحتوى"""
        self.content_thread = threading.Thread(
            target=self.content_scheduler_loop,
            daemon=True
        )
        self.content_thread.start()
    
    def content_scheduler_loop(self):
        """حلقة جدولة المحتوى"""
        while self.is_running:
            try:
                if self.settings.get("enabled", True):
                    self.update_content_queue()
                    self.display_next_content()
                
                time.sleep(self.settings.get("rotation_interval", 15))
                
            except Exception as e:
                print(f"خطأ في جدولة المحتوى: {e}")
                time.sleep(5)
    
    def update_content_queue(self):
        """تحديث طابور المحتوى"""
        try:
            current_time = datetime.now()
            active_content = []
            
            for content_item in self.content_items.values():
                if self.is_content_active(content_item, current_time):
                    active_content.append(content_item)
            
            # ترتيب حسب الأولوية والوقت
            active_content.sort(
                key=lambda x: (x.priority.value, x.created_at),
                reverse=True
            )
            
            self.content_queue = active_content
            
        except Exception as e:
            print(f"خطأ في تحديث طابور المحتوى: {e}")
    
    def is_content_active(self, content_item: ContentItem, current_time: datetime) -> bool:
        """التحقق من نشاط المحتوى"""
        try:
            # التحقق من الحالة
            if content_item.status != ContentStatus.ACTIVE:
                return False
            
            # التحقق من وقت البداية
            if content_item.start_time and current_time < content_item.start_time:
                return False
            
            # التحقق من وقت النهاية
            if content_item.end_time and current_time > content_item.end_time:
                return False
            
            # التحقق من جدولة التكرار
            if content_item.repeat_schedule:
                return self.check_repeat_schedule(content_item.repeat_schedule, current_time)
            
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من نشاط المحتوى: {e}")
            return False
    
    def check_repeat_schedule(self, schedule: Dict, current_time: datetime) -> bool:
        """التحقق من جدولة التكرار"""
        try:
            # التحقق من الأيام
            if "days" in schedule:
                current_day = current_time.strftime("%A").lower()
                if current_day not in [day.lower() for day in schedule["days"]]:
                    return False
            
            # التحقق من الساعات
            if "hours" in schedule:
                current_hour = current_time.hour
                if current_hour not in schedule["hours"]:
                    return False
            
            # التحقق من التواريخ
            if "dates" in schedule:
                current_date = current_time.strftime("%Y-%m-%d")
                if current_date not in schedule["dates"]:
                    return False
            
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من جدولة التكرار: {e}")
            return False
    
    def display_next_content(self):
        """عرض المحتوى التالي"""
        try:
            if not self.content_queue:
                return
            
            # اختيار المحتوى التالي
            next_content = self.content_queue.pop(0)
            self.content_queue.append(next_content)  # إعادة إضافة للنهاية
            
            # عرض المحتوى
            self.display_content(next_content)
            
            # تحديث إحصائيات العرض
            next_content.view_count += 1
            
        except Exception as e:
            print(f"خطأ في عرض المحتوى: {e}")
    
    def display_content(self, content_item: ContentItem):
        """عرض عنصر المحتوى"""
        try:
            if content_item.type == ContentType.IMAGE:
                self.display_image_content(content_item)
            elif content_item.type == ContentType.VIDEO:
                self.display_video_content(content_item)
            elif content_item.type == ContentType.TEXT:
                self.display_text_content(content_item)
            elif content_item.type == ContentType.ANNOUNCEMENT:
                self.display_announcement_content(content_item)
            
            self.current_content = content_item
            print(f"📺 عرض محتوى: {content_item.title}")
            
        except Exception as e:
            print(f"خطأ في عرض المحتوى: {e}")
    
    def display_image_content(self, content_item: ContentItem):
        """عرض محتوى الصورة"""
        try:
            if not self.display_area:
                return
            
            image_path = content_item.data
            
            # تحميل الصورة من الكاش أو من الملف
            if image_path not in self.image_cache:
                image = Image.open(image_path)
                
                # تغيير حجم الصورة حسب الحاجة
                max_size = self.settings["max_image_size"]
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # تحويل لـ PhotoImage
                photo = ImageTk.PhotoImage(image)
                self.image_cache[image_path] = photo
            else:
                photo = self.image_cache[image_path]
            
            # عرض الصورة
            self.display_area.configure(image=photo)
            self.display_area.image = photo  # حفظ مرجع
            
        except Exception as e:
            print(f"خطأ في عرض الصورة: {e}")
    
    def display_video_content(self, content_item: ContentItem):
        """عرض محتوى الفيديو"""
        try:
            # هذا يحتاج مكتبة متخصصة لعرض الفيديو في tkinter
            # يمكن استخدام opencv أو vlc-python
            video_path = content_item.data
            
            # للتبسيط، سنعرض إطار من الفيديو
            cap = cv2.VideoCapture(video_path)
            ret, frame = cap.read()
            
            if ret:
                # تحويل من BGR إلى RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # تحويل إلى PIL Image
                image = Image.fromarray(frame_rgb)
                
                # تغيير الحجم
                max_size = self.settings["max_image_size"]
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # تحويل لـ PhotoImage
                photo = ImageTk.PhotoImage(image)
                
                # عرض الإطار
                if self.display_area:
                    self.display_area.configure(image=photo)
                    self.display_area.image = photo
            
            cap.release()
            
        except Exception as e:
            print(f"خطأ في عرض الفيديو: {e}")
    
    def display_text_content(self, content_item: ContentItem):
        """عرض المحتوى النصي"""
        try:
            if not self.display_area:
                return
            
            text_data = content_item.data
            
            # إعداد النص
            text = text_data.get("text", "")
            font_size = text_data.get("font_size", 16)
            color = text_data.get("color", "white")
            bg_color = text_data.get("bg_color", "transparent")
            
            # عرض النص
            self.display_area.configure(
                text=text,
                font=("Arial", font_size),
                fg=color,
                bg=bg_color if bg_color != "transparent" else self.display_area.cget("bg")
            )
            
        except Exception as e:
            print(f"خطأ في عرض النص: {e}")
    
    def display_announcement_content(self, content_item: ContentItem):
        """عرض محتوى الإعلان"""
        try:
            # إعلان خاص مع تأثيرات بصرية
            announcement_data = content_item.data
            
            # إنشاء نافذة إعلان منبثقة
            announcement_window = tk.Toplevel(self.parent)
            announcement_window.title("إعلان مهم")
            announcement_window.geometry("600x400")
            announcement_window.configure(bg='#FF6B35')
            announcement_window.attributes('-topmost', True)
            
            # توسيط النافذة
            self.center_window(announcement_window, 600, 400)
            
            # محتوى الإعلان
            title_label = tk.Label(
                announcement_window,
                text=announcement_data.get("title", "إعلان"),
                font=("Arial", 24, "bold"),
                bg='#FF6B35',
                fg='white'
            )
            title_label.pack(pady=20)
            
            message_label = tk.Label(
                announcement_window,
                text=announcement_data.get("message", ""),
                font=("Arial", 16),
                bg='#FF6B35',
                fg='white',
                wraplength=500,
                justify=tk.CENTER
            )
            message_label.pack(pady=20)
            
            # إغلاق تلقائي
            announcement_window.after(
                content_item.duration * 1000,
                announcement_window.destroy
            )
            
        except Exception as e:
            print(f"خطأ في عرض الإعلان: {e}")
    
    def center_window(self, window, width, height):
        """توسيط النافذة"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    def set_display_area(self, display_widget):
        """تعيين منطقة العرض"""
        self.display_area = display_widget
    
    def content_item_to_dict(self, content_item: ContentItem) -> Dict:
        """تحويل عنصر المحتوى إلى قاموس"""
        return {
            "id": content_item.id,
            "title": content_item.title,
            "type": content_item.type.value,
            "data": content_item.data,
            "priority": content_item.priority.value,
            "duration": content_item.duration,
            "start_time": content_item.start_time.isoformat() if content_item.start_time else None,
            "end_time": content_item.end_time.isoformat() if content_item.end_time else None,
            "repeat_schedule": content_item.repeat_schedule,
            "tags": content_item.tags,
            "metadata": content_item.metadata,
            "created_at": content_item.created_at.isoformat(),
            "updated_at": content_item.updated_at.isoformat(),
            "view_count": content_item.view_count,
            "status": content_item.status.value
        }
    
    def dict_to_content_item(self, data: Dict) -> ContentItem:
        """تحويل قاموس إلى عنصر محتوى"""
        content_item = ContentItem(
            content_id=data["id"],
            title=data["title"],
            content_type=ContentType(data["type"]),
            data=data["data"],
            priority=ContentPriority(data["priority"]),
            duration=data["duration"],
            start_time=datetime.fromisoformat(data["start_time"]) if data["start_time"] else None,
            end_time=datetime.fromisoformat(data["end_time"]) if data["end_time"] else None,
            repeat_schedule=data["repeat_schedule"],
            tags=data["tags"],
            metadata=data["metadata"]
        )
        
        content_item.created_at = datetime.fromisoformat(data["created_at"])
        content_item.updated_at = datetime.fromisoformat(data["updated_at"])
        content_item.view_count = data["view_count"]
        content_item.status = ContentStatus(data["status"])
        
        return content_item
    
    def stop(self):
        """إيقاف نظام إدارة المحتوى"""
        self.is_running = False
        self.save_content_items()
        print("🛑 تم إيقاف نظام إدارة المحتوى")
