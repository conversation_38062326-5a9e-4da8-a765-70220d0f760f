// Azan Service for playing prayer call audio

class AzanService {
  constructor() {
    this.audio = null;
    this.isPlaying = false;
    this.volume = 0.8;
    this.azanFiles = {
      fajr: '/audio/azan-fajr.mp3',
      dhuhr: '/audio/azan-normal.mp3',
      asr: '/audio/azan-normal.mp3',
      maghrib: '/audio/azan-normal.mp3',
      isha: '/audio/azan-normal.mp3'
    };
  }

  // Initialize audio context (required for autoplay in modern browsers)
  async initializeAudio() {
    try {
      // Create a silent audio context to enable autoplay
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
      }
      return true;
    } catch (error) {
      console.error('Failed to initialize audio context:', error);
      return false;
    }
  }

  // Play azan for specific prayer
  async playAzan(prayerName) {
    try {
      // Stop any currently playing azan
      this.stopAzan();

      const azanFile = this.azanFiles[prayerName] || this.azanFiles.dhuhr;
      
      // For demo purposes, we'll use a text-to-speech announcement
      // In production, you would load actual azan audio files
      await this.playAzanAnnouncement(prayerName);
      
      // Uncomment below for actual audio file playback
      // this.audio = new Audio(azanFile);
      // this.audio.volume = this.volume;
      // this.audio.loop = false;
      // 
      // this.audio.onended = () => {
      //   this.isPlaying = false;
      // };
      // 
      // this.audio.onerror = (error) => {
      //   console.error('Error playing azan:', error);
      //   this.isPlaying = false;
      // };
      // 
      // await this.audio.play();
      // this.isPlaying = true;

    } catch (error) {
      console.error('Failed to play azan:', error);
      this.isPlaying = false;
    }
  }

  // Play azan announcement using text-to-speech (for demo)
  async playAzanAnnouncement(prayerName) {
    if ('speechSynthesis' in window) {
      const prayerNames = {
        fajr: 'حان الآن موعد صلاة الفجر',
        dhuhr: 'حان الآن موعد صلاة الظهر',
        asr: 'حان الآن موعد صلاة العصر',
        maghrib: 'حان الآن موعد صلاة المغرب',
        isha: 'حان الآن موعد صلاة العشاء'
      };

      const utterance = new SpeechSynthesisUtterance(prayerNames[prayerName]);
      utterance.lang = 'ar-SA';
      utterance.rate = 0.8;
      utterance.pitch = 1;
      utterance.volume = this.volume;

      this.isPlaying = true;
      
      utterance.onend = () => {
        this.isPlaying = false;
      };

      utterance.onerror = () => {
        this.isPlaying = false;
      };

      speechSynthesis.speak(utterance);
    }
  }

  // Stop currently playing azan
  stopAzan() {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
      this.audio = null;
    }
    
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
    }
    
    this.isPlaying = false;
  }

  // Set volume (0.0 to 1.0)
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    if (this.audio) {
      this.audio.volume = this.volume;
    }
  }

  // Check if azan is currently playing
  getIsPlaying() {
    return this.isPlaying;
  }

  // Schedule azan for prayer times
  scheduleAzan(prayerTimes) {
    // Clear existing timeouts
    this.clearScheduledAzans();
    
    const now = new Date();
    const scheduledAzans = [];

    Object.entries(prayerTimes).forEach(([prayer, times]) => {
      if (prayer === 'sunrise') return; // No azan for sunrise
      
      const azanTime = times.azan;
      const [hours, minutes] = azanTime.split(':').map(Number);
      
      const azanDate = new Date(now);
      azanDate.setHours(hours, minutes, 0, 0);
      
      // If time has passed today, schedule for tomorrow
      if (azanDate <= now) {
        azanDate.setDate(azanDate.getDate() + 1);
      }
      
      const timeUntilAzan = azanDate.getTime() - now.getTime();
      
      if (timeUntilAzan > 0) {
        const timeoutId = setTimeout(() => {
          this.playAzan(prayer);
        }, timeUntilAzan);
        
        scheduledAzans.push({
          prayer,
          time: azanTime,
          timeoutId,
          scheduledFor: azanDate
        });
      }
    });

    this.scheduledAzans = scheduledAzans;
    return scheduledAzans;
  }

  // Clear all scheduled azans
  clearScheduledAzans() {
    if (this.scheduledAzans) {
      this.scheduledAzans.forEach(azan => {
        clearTimeout(azan.timeoutId);
      });
    }
    this.scheduledAzans = [];
  }

  // Get next scheduled azan
  getNextScheduledAzan() {
    if (!this.scheduledAzans || this.scheduledAzans.length === 0) {
      return null;
    }
    
    const now = new Date();
    return this.scheduledAzans
      .filter(azan => azan.scheduledFor > now)
      .sort((a, b) => a.scheduledFor - b.scheduledFor)[0];
  }
}

// Create singleton instance
const azanService = new AzanService();

export default azanService;
