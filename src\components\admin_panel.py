#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة التحكم الإدارية
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import arabic_reshaper
from bidi.algorithm import get_display

class AdminPanel(tk.Toplevel):
    """نافذة لوحة التحكم الإدارية"""
    
    def __init__(self, parent, app):
        super().__init__(parent)
        self.app = app
        self.settings = self.load_settings()
        
        self.setup_window()
        self.setup_ui()
        self.load_current_settings()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.title("لوحة التحكم الإدارية")
        self.geometry("800x600")
        self.configure(bg='#2d2d2d')
        self.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.transient(self.master)
        self.grab_set()
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.winfo_screenheight() // 2) - (600 // 2)
        self.geometry(f"800x600+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self, bg='#8B0000', height=60)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text=self.format_arabic_text("لوحة التحكم الإدارية"),
            font=self.app.get_font('title'),
            bg='#8B0000',
            fg='white'
        )
        title_label.pack(expand=True)
        
        # إطار المحتوى
        content_frame = tk.Frame(self, bg='#2d2d2d')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب أوقات الصلاة
        self.prayer_times_tab = tk.Frame(self.notebook, bg='#3d3d3d')
        self.notebook.add(self.prayer_times_tab, text="أوقات الصلاة")
        self.setup_prayer_times_tab()
        
        # تبويب إعدادات الصوت
        self.audio_tab = tk.Frame(self.notebook, bg='#3d3d3d')
        self.notebook.add(self.audio_tab, text="إعدادات الصوت")
        self.setup_audio_tab()
        
        # تبويب النصوص الإسلامية
        self.texts_tab = tk.Frame(self.notebook, bg='#3d3d3d')
        self.notebook.add(self.texts_tab, text="النصوص الإسلامية")
        self.setup_texts_tab()
        
        # تبويب الإعدادات العامة
        self.general_tab = tk.Frame(self.notebook, bg='#3d3d3d')
        self.notebook.add(self.general_tab, text="إعدادات عامة")
        self.setup_general_tab()
        
        # أزرار التحكم
        self.setup_control_buttons(content_frame)
    
    def setup_prayer_times_tab(self):
        """إعداد تبويب أوقات الصلاة"""
        # إطار التمرير
        canvas = tk.Canvas(self.prayer_times_tab, bg='#3d3d3d')
        scrollbar = ttk.Scrollbar(self.prayer_times_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#3d3d3d')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # عنوان القسم
        title_label = tk.Label(
            scrollable_frame,
            text=self.format_arabic_text("تعديل أوقات الصلاة والإقامة"),
            font=self.app.get_font('medium'),
            bg='#3d3d3d',
            fg='white'
        )
        title_label.pack(pady=(10, 20))
        
        # إنشاء حقول أوقات الصلاة
        self.prayer_entries = {}
        prayers = [
            ('الفجر', 'fajr'),
            ('الشروق', 'sunrise'),
            ('الظهر', 'dhuhr'),
            ('العصر', 'asr'),
            ('المغرب', 'maghrib'),
            ('العشاء', 'isha')
        ]
        
        for prayer_name, prayer_key in prayers:
            self.create_prayer_time_row(scrollable_frame, prayer_name, prayer_key)
    
    def create_prayer_time_row(self, parent, prayer_name, prayer_key):
        """إنشاء صف لتعديل وقت صلاة"""
        # إطار الصف
        row_frame = tk.Frame(parent, bg='#4d4d4d', relief=tk.RAISED, bd=2)
        row_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # اسم الصلاة
        name_label = tk.Label(
            row_frame,
            text=self.format_arabic_text(prayer_name),
            font=self.app.get_font('medium'),
            bg='#4d4d4d',
            fg='white',
            width=10
        )
        name_label.grid(row=0, column=0, padx=10, pady=10, sticky='w')
        
        # وقت الأذان
        azan_label = tk.Label(
            row_frame,
            text=self.format_arabic_text("الأذان:"),
            font=self.app.get_font('small'),
            bg='#4d4d4d',
            fg='white'
        )
        azan_label.grid(row=0, column=1, padx=5, pady=10)
        
        azan_entry = tk.Entry(
            row_frame,
            font=self.app.get_font('small'),
            width=8,
            justify='center'
        )
        azan_entry.grid(row=0, column=2, padx=5, pady=10)
        
        # وقت الإقامة
        iqama_label = tk.Label(
            row_frame,
            text=self.format_arabic_text("الإقامة:"),
            font=self.app.get_font('small'),
            bg='#4d4d4d',
            fg='white'
        )
        iqama_label.grid(row=0, column=3, padx=5, pady=10)
        
        iqama_entry = tk.Entry(
            row_frame,
            font=self.app.get_font('small'),
            width=8,
            justify='center'
        )
        iqama_entry.grid(row=0, column=4, padx=5, pady=10)
        
        # زر اختبار الأذان
        test_button = tk.Button(
            row_frame,
            text=self.format_arabic_text("تجربة"),
            font=self.app.get_font('small'),
            bg='#228B22',
            fg='white',
            command=lambda: self.test_azan(prayer_key)
        )
        test_button.grid(row=0, column=5, padx=10, pady=10)
        
        # حفظ المراجع
        self.prayer_entries[prayer_key] = {
            'azan': azan_entry,
            'iqama': iqama_entry
        }
        
        # توزيع الأعمدة
        row_frame.grid_columnconfigure(0, weight=1)
        row_frame.grid_columnconfigure(1, weight=0)
        row_frame.grid_columnconfigure(2, weight=0)
        row_frame.grid_columnconfigure(3, weight=0)
        row_frame.grid_columnconfigure(4, weight=0)
        row_frame.grid_columnconfigure(5, weight=0)
    
    def setup_audio_tab(self):
        """إعداد تبويب إعدادات الصوت"""
        # عنوان القسم
        title_label = tk.Label(
            self.audio_tab,
            text=self.format_arabic_text("إعدادات الصوت والأذان"),
            font=self.app.get_font('medium'),
            bg='#3d3d3d',
            fg='white'
        )
        title_label.pack(pady=(20, 30))
        
        # مستوى الصوت
        volume_frame = tk.Frame(self.audio_tab, bg='#3d3d3d')
        volume_frame.pack(fill=tk.X, padx=20, pady=10)
        
        volume_label = tk.Label(
            volume_frame,
            text=self.format_arabic_text("مستوى الصوت:"),
            font=self.app.get_font('medium'),
            bg='#3d3d3d',
            fg='white'
        )
        volume_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.volume_var = tk.DoubleVar(value=0.8)
        self.volume_scale = tk.Scale(
            volume_frame,
            from_=0.0,
            to=1.0,
            resolution=0.1,
            orient=tk.HORIZONTAL,
            variable=self.volume_var,
            command=self.on_volume_change,
            bg='#4d4d4d',
            fg='white',
            highlightthickness=0
        )
        self.volume_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)
        
        self.volume_label = tk.Label(
            volume_frame,
            text="80%",
            font=self.app.get_font('small'),
            bg='#3d3d3d',
            fg='white'
        )
        self.volume_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # أزرار اختبار الصوت
        test_frame = tk.Frame(self.audio_tab, bg='#3d3d3d')
        test_frame.pack(pady=30)
        
        test_azan_button = tk.Button(
            test_frame,
            text=self.format_arabic_text("تجربة الأذان"),
            font=self.app.get_font('medium'),
            bg='#4169E1',
            fg='white',
            padx=20,
            pady=10,
            command=self.test_general_azan
        )
        test_azan_button.pack(side=tk.LEFT, padx=10)
        
        stop_button = tk.Button(
            test_frame,
            text=self.format_arabic_text("إيقاف الصوت"),
            font=self.app.get_font('medium'),
            bg='#DC143C',
            fg='white',
            padx=20,
            pady=10,
            command=self.stop_azan
        )
        stop_button.pack(side=tk.LEFT, padx=10)
    
    def setup_texts_tab(self):
        """إعداد تبويب النصوص الإسلامية"""
        # عنوان القسم
        title_label = tk.Label(
            self.texts_tab,
            text=self.format_arabic_text("إدارة النصوص الإسلامية"),
            font=self.app.get_font('medium'),
            bg='#3d3d3d',
            fg='white'
        )
        title_label.pack(pady=(20, 20))
        
        # إضافة نص جديد
        add_frame = tk.Frame(self.texts_tab, bg='#3d3d3d')
        add_frame.pack(fill=tk.X, padx=20, pady=10)
        
        add_label = tk.Label(
            add_frame,
            text=self.format_arabic_text("إضافة نص جديد:"),
            font=self.app.get_font('medium'),
            bg='#3d3d3d',
            fg='white'
        )
        add_label.pack(anchor='w')
        
        self.new_text_entry = tk.Text(
            add_frame,
            height=3,
            font=self.app.get_font('small'),
            wrap=tk.WORD
        )
        self.new_text_entry.pack(fill=tk.X, pady=(5, 10))
        
        add_button = tk.Button(
            add_frame,
            text=self.format_arabic_text("إضافة النص"),
            font=self.app.get_font('medium'),
            bg='#228B22',
            fg='white',
            command=self.add_islamic_text
        )
        add_button.pack()
        
        # قائمة النصوص الحالية
        list_label = tk.Label(
            self.texts_tab,
            text=self.format_arabic_text("النصوص الحالية:"),
            font=self.app.get_font('medium'),
            bg='#3d3d3d',
            fg='white'
        )
        list_label.pack(anchor='w', padx=20, pady=(20, 10))
        
        # إطار قائمة النصوص
        list_frame = tk.Frame(self.texts_tab, bg='#3d3d3d')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        
        # قائمة النصوص مع شريط التمرير
        self.texts_listbox = tk.Listbox(
            list_frame,
            font=self.app.get_font('small'),
            bg='#4d4d4d',
            fg='white',
            selectbackground='#DC143C'
        )
        
        scrollbar_texts = ttk.Scrollbar(list_frame, orient="vertical")
        self.texts_listbox.config(yscrollcommand=scrollbar_texts.set)
        scrollbar_texts.config(command=self.texts_listbox.yview)
        
        self.texts_listbox.pack(side="left", fill="both", expand=True)
        scrollbar_texts.pack(side="right", fill="y")
        
        # زر حذف النص
        delete_button = tk.Button(
            self.texts_tab,
            text=self.format_arabic_text("حذف النص المحدد"),
            font=self.app.get_font('medium'),
            bg='#DC143C',
            fg='white',
            command=self.delete_islamic_text
        )
        delete_button.pack(pady=10)
    
    def setup_general_tab(self):
        """إعداد تبويب الإعدادات العامة"""
        # عنوان القسم
        title_label = tk.Label(
            self.general_tab,
            text=self.format_arabic_text("الإعدادات العامة"),
            font=self.app.get_font('medium'),
            bg='#3d3d3d',
            fg='white'
        )
        title_label.pack(pady=(20, 30))
        
        # اسم المسجد
        mosque_frame = tk.Frame(self.general_tab, bg='#3d3d3d')
        mosque_frame.pack(fill=tk.X, padx=20, pady=20)
        
        mosque_label = tk.Label(
            mosque_frame,
            text=self.format_arabic_text("اسم المسجد:"),
            font=self.app.get_font('medium'),
            bg='#3d3d3d',
            fg='white'
        )
        mosque_label.pack(anchor='w')
        
        self.mosque_name_entry = tk.Entry(
            mosque_frame,
            font=self.app.get_font('medium'),
            width=40
        )
        self.mosque_name_entry.pack(fill=tk.X, pady=(5, 0))
        
        # الموقع الجغرافي
        location_frame = tk.Frame(self.general_tab, bg='#3d3d3d')
        location_frame.pack(fill=tk.X, padx=20, pady=20)
        
        location_label = tk.Label(
            location_frame,
            text=self.format_arabic_text("الموقع الجغرافي:"),
            font=self.app.get_font('medium'),
            bg='#3d3d3d',
            fg='white'
        )
        location_label.pack(anchor='w')
        
        coords_frame = tk.Frame(location_frame, bg='#3d3d3d')
        coords_frame.pack(fill=tk.X, pady=(5, 0))
        
        lat_label = tk.Label(
            coords_frame,
            text="خط العرض:",
            font=self.app.get_font('small'),
            bg='#3d3d3d',
            fg='white'
        )
        lat_label.pack(side=tk.LEFT)
        
        self.latitude_entry = tk.Entry(
            coords_frame,
            font=self.app.get_font('small'),
            width=15
        )
        self.latitude_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        lng_label = tk.Label(
            coords_frame,
            text="خط الطول:",
            font=self.app.get_font('small'),
            bg='#3d3d3d',
            fg='white'
        )
        lng_label.pack(side=tk.LEFT)
        
        self.longitude_entry = tk.Entry(
            coords_frame,
            font=self.app.get_font('small'),
            width=15
        )
        self.longitude_entry.pack(side=tk.LEFT, padx=(5, 0))
    
    def setup_control_buttons(self, parent):
        """إعداد أزرار التحكم"""
        buttons_frame = tk.Frame(parent, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # زر الحفظ
        save_button = tk.Button(
            buttons_frame,
            text=self.format_arabic_text("حفظ الإعدادات"),
            font=self.app.get_font('medium'),
            bg='#228B22',
            fg='white',
            padx=20,
            pady=10,
            command=self.save_settings
        )
        save_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        # زر الإلغاء
        cancel_button = tk.Button(
            buttons_frame,
            text=self.format_arabic_text("إلغاء"),
            font=self.app.get_font('medium'),
            bg='#666666',
            fg='white',
            padx=20,
            pady=10,
            command=self.destroy
        )
        cancel_button.pack(side=tk.RIGHT)
    
    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        # تحميل أوقات الصلاة
        if hasattr(self.app, 'prayer_times') and self.app.prayer_times:
            for prayer_key, times in self.app.prayer_times.items():
                if prayer_key in self.prayer_entries:
                    self.prayer_entries[prayer_key]['azan'].insert(0, times.get('azan', ''))
                    self.prayer_entries[prayer_key]['iqama'].insert(0, times.get('iqama', ''))
        
        # تحميل مستوى الصوت
        if hasattr(self.app, 'azan_service'):
            self.volume_var.set(self.app.azan_service.volume)
            self.on_volume_change(self.app.azan_service.volume)
        
        # تحميل النصوص الإسلامية
        if hasattr(self.app, 'islamic_texts'):
            for text in self.app.islamic_texts:
                self.texts_listbox.insert(tk.END, text)
        
        # تحميل الإعدادات العامة
        self.mosque_name_entry.insert(0, self.settings.get('mosque_name', 'اسم المسجد'))
        self.latitude_entry.insert(0, str(self.settings.get('latitude', 36.7538)))
        self.longitude_entry.insert(0, str(self.settings.get('longitude', 3.0588)))
    
    def on_volume_change(self, value):
        """تغيير مستوى الصوت"""
        volume = float(value)
        self.volume_label.config(text=f"{int(volume * 100)}%")
        
        if hasattr(self.app, 'azan_service'):
            self.app.azan_service.set_volume(volume)
    
    def test_azan(self, prayer_name):
        """اختبار أذان صلاة معينة"""
        if hasattr(self.app, 'azan_service'):
            self.app.azan_service.test_azan(prayer_name)
    
    def test_general_azan(self):
        """اختبار الأذان العام"""
        if hasattr(self.app, 'azan_service'):
            self.app.azan_service.test_azan('dhuhr')
    
    def stop_azan(self):
        """إيقاف الأذان"""
        if hasattr(self.app, 'azan_service'):
            self.app.azan_service.stop_azan()
    
    def add_islamic_text(self):
        """إضافة نص إسلامي جديد"""
        new_text = self.new_text_entry.get("1.0", tk.END).strip()
        if new_text:
            self.texts_listbox.insert(tk.END, new_text)
            self.new_text_entry.delete("1.0", tk.END)
    
    def delete_islamic_text(self):
        """حذف النص الإسلامي المحدد"""
        selection = self.texts_listbox.curselection()
        if selection:
            self.texts_listbox.delete(selection[0])
    
    def save_settings(self):
        """حفظ جميع الإعدادات"""
        try:
            # حفظ أوقات الصلاة
            new_prayer_times = {}
            for prayer_key, entries in self.prayer_entries.items():
                azan_time = entries['azan'].get().strip()
                iqama_time = entries['iqama'].get().strip()
                
                if azan_time and iqama_time:
                    new_prayer_times[prayer_key] = {
                        'azan': azan_time,
                        'iqama': iqama_time
                    }
            
            # تحديث أوقات الصلاة في التطبيق
            if new_prayer_times:
                self.app.prayer_times = new_prayer_times
                if hasattr(self.app, 'azan_service'):
                    self.app.azan_service.schedule_azan(new_prayer_times)
            
            # حفظ النصوص الإسلامية
            islamic_texts = []
            for i in range(self.texts_listbox.size()):
                islamic_texts.append(self.texts_listbox.get(i))
            
            if islamic_texts:
                self.app.islamic_texts = islamic_texts
            
            # حفظ الإعدادات العامة
            mosque_name = self.mosque_name_entry.get().strip()
            if mosque_name:
                self.settings['mosque_name'] = mosque_name
                if hasattr(self.app, 'header_frame'):
                    self.app.header_frame.update_mosque_name(mosque_name)
            
            # حفظ الموقع الجغرافي
            try:
                latitude = float(self.latitude_entry.get())
                longitude = float(self.longitude_entry.get())
                self.settings['latitude'] = latitude
                self.settings['longitude'] = longitude
                
                # تحديث الموقع في الخدمات
                if hasattr(self.app, 'prayer_service'):
                    self.app.prayer_service.set_location(latitude, longitude)
                if hasattr(self.app, 'weather_service'):
                    self.app.weather_service.set_location(latitude, longitude)
                    
            except ValueError:
                pass  # تجاهل الأخطاء في الإحداثيات
            
            # حفظ الإعدادات في ملف
            self.save_settings_to_file()
            
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
            self.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {e}")
    
    def load_settings(self):
        """تحميل الإعدادات من ملف"""
        try:
            with open('settings.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {
                'mosque_name': 'اسم المسجد',
                'latitude': 36.7538,
                'longitude': 3.0588,
                'volume': 0.8
            }
    
    def save_settings_to_file(self):
        """حفظ الإعدادات في ملف"""
        try:
            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ ملف الإعدادات: {e}")
