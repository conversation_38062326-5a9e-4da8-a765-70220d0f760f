#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خدمة حساب أوقات الصلاة
"""

import requests
import json
from datetime import datetime, timedelta
import math

class PrayerTimesService:
    """خدمة حساب أوقات الصلاة"""
    
    def __init__(self):
        self.latitude = 36.7538  # الجزائر العاصمة (افتراضي)
        self.longitude = 3.0588
        self.method = 2  # طريقة الحساب (الجامعة الإسلامية في أم القرى)
        self.cache_file = "prayer_times_cache.json"
        self.last_update = None
        self.cached_times = None
    
    def set_location(self, latitude, longitude):
        """تحديد الموقع الجغرافي"""
        self.latitude = latitude
        self.longitude = longitude
        self.cached_times = None  # إعادة تعيين الكاش
    
    def get_prayer_times(self):
        """الحصول على أوقات الصلاة"""
        try:
            # التحقق من الكاش
            if self.is_cache_valid():
                return self.cached_times
            
            # جلب الأوقات من API
            times = self.fetch_from_api()
            if times:
                self.cached_times = times
                self.last_update = datetime.now()
                self.save_cache()
                return times
            
            # في حالة فشل API، استخدام الحساب المحلي
            return self.calculate_locally()
            
        except Exception as e:
            print(f"خطأ في جلب أوقات الصلاة: {e}")
            return self.get_default_times()
    
    def fetch_from_api(self):
        """جلب الأوقات من AlAdhan API"""
        try:
            today = datetime.now()
            timestamp = int(today.timestamp())
            
            url = f"https://api.aladhan.com/v1/timings/{timestamp}"
            params = {
                'latitude': self.latitude,
                'longitude': self.longitude,
                'method': self.method
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            timings = data['data']['timings']
            
            # تحويل الأوقات وإضافة أوقات الإقامة
            prayer_times = {
                'fajr': {
                    'azan': self.convert_to_24h(timings['Fajr']),
                    'iqama': self.add_minutes(self.convert_to_24h(timings['Fajr']), 10)
                },
                'sunrise': {
                    'azan': self.convert_to_24h(timings['Sunrise']),
                    'iqama': self.convert_to_24h(timings['Sunrise'])
                },
                'dhuhr': {
                    'azan': self.convert_to_24h(timings['Dhuhr']),
                    'iqama': self.add_minutes(self.convert_to_24h(timings['Dhuhr']), 10)
                },
                'asr': {
                    'azan': self.convert_to_24h(timings['Asr']),
                    'iqama': self.add_minutes(self.convert_to_24h(timings['Asr']), 10)
                },
                'maghrib': {
                    'azan': self.convert_to_24h(timings['Maghrib']),
                    'iqama': self.add_minutes(self.convert_to_24h(timings['Maghrib']), 10)
                },
                'isha': {
                    'azan': self.convert_to_24h(timings['Isha']),
                    'iqama': self.add_minutes(self.convert_to_24h(timings['Isha']), 10)
                }
            }
            
            return prayer_times
            
        except Exception as e:
            print(f"خطأ في جلب البيانات من API: {e}")
            return None
    
    def calculate_locally(self):
        """حساب الأوقات محلياً (حساب فلكي مبسط)"""
        try:
            today = datetime.now()
            
            # حساب مبسط للأوقات (يحتاج تحسين للدقة)
            base_times = {
                'fajr': '04:30',
                'sunrise': '06:00',
                'dhuhr': '12:30',
                'asr': '15:45',
                'maghrib': '18:30',
                'isha': '20:00'
            }
            
            # تعديل بسيط حسب خط العرض
            lat_adjustment = (self.latitude - 35) * 2  # دقائق
            
            prayer_times = {}
            for prayer, time_str in base_times.items():
                adjusted_time = self.add_minutes(time_str, lat_adjustment)
                prayer_times[prayer] = {
                    'azan': adjusted_time,
                    'iqama': self.add_minutes(adjusted_time, 10) if prayer != 'sunrise' else adjusted_time
                }
            
            return prayer_times
            
        except Exception as e:
            print(f"خطأ في الحساب المحلي: {e}")
            return self.get_default_times()
    
    def get_default_times(self):
        """الأوقات الافتراضية"""
        return {
            'fajr': {'azan': '04:11', 'iqama': '04:21'},
            'sunrise': {'azan': '05:37', 'iqama': '05:52'},
            'dhuhr': {'azan': '12:20', 'iqama': '12:30'},
            'asr': {'azan': '15:37', 'iqama': '15:47'},
            'maghrib': {'azan': '19:01', 'iqama': '19:11'},
            'isha': {'azan': '20:31', 'iqama': '20:41'}
        }
    
    def convert_to_24h(self, time_12h):
        """تحويل من 12 ساعة إلى 24 ساعة"""
        try:
            if ' ' in time_12h:
                time_part, period = time_12h.split(' ')
            else:
                return time_12h  # افتراض أنه بصيغة 24 ساعة
            
            hours, minutes = time_part.split(':')
            hours = int(hours)
            minutes = int(minutes)
            
            if period.upper() == 'PM' and hours != 12:
                hours += 12
            elif period.upper() == 'AM' and hours == 12:
                hours = 0
            
            return f"{hours:02d}:{minutes:02d}"
            
        except Exception:
            return time_12h
    
    def add_minutes(self, time_str, minutes_to_add):
        """إضافة دقائق لوقت معين"""
        try:
            hours, minutes = map(int, time_str.split(':'))
            total_minutes = hours * 60 + minutes + minutes_to_add
            
            new_hours = (total_minutes // 60) % 24
            new_minutes = total_minutes % 60
            
            return f"{new_hours:02d}:{new_minutes:02d}"
            
        except Exception:
            return time_str
    
    def is_cache_valid(self):
        """التحقق من صحة الكاش"""
        if not self.cached_times or not self.last_update:
            return False
        
        # الكاش صالح لمدة ساعة
        time_diff = datetime.now() - self.last_update
        return time_diff.total_seconds() < 3600
    
    def save_cache(self):
        """حفظ الكاش في ملف"""
        try:
            cache_data = {
                'times': self.cached_times,
                'last_update': self.last_update.isoformat(),
                'location': {
                    'latitude': self.latitude,
                    'longitude': self.longitude
                }
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ الكاش: {e}")
    
    def load_cache(self):
        """تحميل الكاش من ملف"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            self.cached_times = cache_data['times']
            self.last_update = datetime.fromisoformat(cache_data['last_update'])
            
            # التحقق من تطابق الموقع
            cached_location = cache_data.get('location', {})
            if (cached_location.get('latitude') != self.latitude or 
                cached_location.get('longitude') != self.longitude):
                self.cached_times = None
                self.last_update = None
                
        except Exception as e:
            print(f"خطأ في تحميل الكاش: {e}")
            self.cached_times = None
            self.last_update = None
