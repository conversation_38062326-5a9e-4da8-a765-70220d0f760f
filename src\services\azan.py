#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خدمة الأذان الصوتي
"""

import pygame
import pyttsx3
import threading
import schedule
import time
from datetime import datetime, timedelta
import os
from pathlib import Path

class AzanService:
    """خدمة تشغيل الأذان"""
    
    def __init__(self):
        self.volume = 0.8
        self.is_playing = False
        self.current_audio = None
        self.tts_engine = None
        self.scheduled_jobs = []
        
        # مسارات ملفات الأذان
        self.audio_dir = Path("audio")
        self.azan_files = {
            'fajr': self.audio_dir / 'azan_fajr.mp3',
            'dhuhr': self.audio_dir / 'azan_normal.mp3',
            'asr': self.audio_dir / 'azan_normal.mp3',
            'maghrib': self.audio_dir / 'azan_normal.mp3',
            'isha': self.audio_dir / 'azan_normal.mp3'
        }
        
        self.initialize_audio()
        self.initialize_tts()
    
    def initialize_audio(self):
        """تهيئة نظام الصوت"""
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            print("تم تهيئة نظام الصوت بنجاح")
        except Exception as e:
            print(f"خطأ في تهيئة نظام الصوت: {e}")
    
    def initialize_tts(self):
        """تهيئة محرك النطق"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # إعداد الصوت
            voices = self.tts_engine.getProperty('voices')
            
            # البحث عن صوت عربي
            arabic_voice = None
            for voice in voices:
                if 'arabic' in voice.name.lower() or 'ar' in voice.id.lower():
                    arabic_voice = voice.id
                    break
            
            if arabic_voice:
                self.tts_engine.setProperty('voice', arabic_voice)
            
            # إعداد السرعة والصوت
            self.tts_engine.setProperty('rate', 150)  # سرعة النطق
            self.tts_engine.setProperty('volume', self.volume)
            
            print("تم تهيئة محرك النطق بنجاح")
            
        except Exception as e:
            print(f"خطأ في تهيئة محرك النطق: {e}")
            self.tts_engine = None
    
    def play_azan(self, prayer_name):
        """تشغيل الأذان لصلاة معينة"""
        if self.is_playing:
            self.stop_azan()
        
        try:
            # محاولة تشغيل ملف صوتي
            if self.play_audio_file(prayer_name):
                return True
            
            # في حالة عدم وجود ملف صوتي، استخدام النطق
            return self.play_azan_announcement(prayer_name)
            
        except Exception as e:
            print(f"خطأ في تشغيل الأذان: {e}")
            return False
    
    def play_audio_file(self, prayer_name):
        """تشغيل ملف صوتي للأذان"""
        try:
            audio_file = self.azan_files.get(prayer_name)
            
            if not audio_file or not audio_file.exists():
                print(f"ملف الأذان غير موجود: {audio_file}")
                return False
            
            # تشغيل الملف الصوتي
            pygame.mixer.music.load(str(audio_file))
            pygame.mixer.music.set_volume(self.volume)
            pygame.mixer.music.play()
            
            self.is_playing = True
            
            # مراقبة انتهاء التشغيل
            threading.Thread(target=self.monitor_playback, daemon=True).start()
            
            print(f"تم تشغيل أذان {prayer_name}")
            return True
            
        except Exception as e:
            print(f"خطأ في تشغيل الملف الصوتي: {e}")
            return False
    
    def play_azan_announcement(self, prayer_name):
        """تشغيل إعلان الأذان بالنطق"""
        if not self.tts_engine:
            return False
        
        try:
            prayer_names = {
                'fajr': 'حان الآن موعد صلاة الفجر',
                'dhuhr': 'حان الآن موعد صلاة الظهر',
                'asr': 'حان الآن موعد صلاة العصر',
                'maghrib': 'حان الآن موعد صلاة المغرب',
                'isha': 'حان الآن موعد صلاة العشاء'
            }
            
            announcement = prayer_names.get(prayer_name, f'حان موعد الصلاة')
            
            self.is_playing = True
            
            # تشغيل الإعلان في خيط منفصل
            def speak():
                try:
                    self.tts_engine.say(announcement)
                    self.tts_engine.runAndWait()
                    self.is_playing = False
                except Exception as e:
                    print(f"خطأ في النطق: {e}")
                    self.is_playing = False
            
            threading.Thread(target=speak, daemon=True).start()
            
            print(f"تم تشغيل إعلان أذان {prayer_name}")
            return True
            
        except Exception as e:
            print(f"خطأ في تشغيل إعلان الأذان: {e}")
            return False
    
    def monitor_playback(self):
        """مراقبة انتهاء تشغيل الملف الصوتي"""
        try:
            while pygame.mixer.music.get_busy():
                time.sleep(0.1)
            self.is_playing = False
            print("انتهى تشغيل الأذان")
        except Exception as e:
            print(f"خطأ في مراقبة التشغيل: {e}")
            self.is_playing = False
    
    def stop_azan(self):
        """إيقاف الأذان"""
        try:
            if pygame.mixer.music.get_busy():
                pygame.mixer.music.stop()
            
            if self.tts_engine:
                self.tts_engine.stop()
            
            self.is_playing = False
            print("تم إيقاف الأذان")
            
        except Exception as e:
            print(f"خطأ في إيقاف الأذان: {e}")
    
    def set_volume(self, volume):
        """تعديل مستوى الصوت"""
        self.volume = max(0.0, min(1.0, volume))
        
        try:
            if pygame.mixer.music.get_busy():
                pygame.mixer.music.set_volume(self.volume)
            
            if self.tts_engine:
                self.tts_engine.setProperty('volume', self.volume)
                
        except Exception as e:
            print(f"خطأ في تعديل مستوى الصوت: {e}")
    
    def schedule_azan(self, prayer_times):
        """جدولة الأذان لأوقات الصلاة"""
        # إلغاء الجدولة السابقة
        self.clear_schedule()
        
        try:
            for prayer, times in prayer_times.items():
                if prayer == 'sunrise':  # لا أذان للشروق
                    continue
                
                azan_time = times.get('azan')
                if azan_time:
                    # جدولة الأذان
                    schedule.every().day.at(azan_time).do(self.play_azan, prayer)
                    self.scheduled_jobs.append(f"{prayer} at {azan_time}")
                    print(f"تم جدولة أذان {prayer} في {azan_time}")
            
            # بدء خيط مراقبة الجدولة
            if not hasattr(self, 'schedule_thread') or not self.schedule_thread.is_alive():
                self.schedule_thread = threading.Thread(target=self.run_schedule, daemon=True)
                self.schedule_thread.start()
                
        except Exception as e:
            print(f"خطأ في جدولة الأذان: {e}")
    
    def run_schedule(self):
        """تشغيل مراقب الجدولة"""
        while True:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                print(f"خطأ في مراقب الجدولة: {e}")
                time.sleep(60)
    
    def clear_schedule(self):
        """إلغاء جميع الجدولات"""
        schedule.clear()
        self.scheduled_jobs = []
        print("تم إلغاء جميع جدولات الأذان")
    
    def test_azan(self, prayer_name='dhuhr'):
        """اختبار تشغيل الأذان"""
        print(f"اختبار أذان {prayer_name}")
        return self.play_azan(prayer_name)
    
    def get_scheduled_jobs(self):
        """الحصول على قائمة الجدولات المفعلة"""
        return self.scheduled_jobs.copy()
    
    def is_audio_playing(self):
        """التحقق من حالة تشغيل الصوت"""
        return self.is_playing
    
    def stop_all(self):
        """إيقاف جميع الخدمات"""
        self.stop_azan()
        self.clear_schedule()
        
        try:
            pygame.mixer.quit()
        except:
            pass
        
        print("تم إيقاف جميع خدمات الأذان")
    
    def create_audio_directory(self):
        """إنشاء مجلد الملفات الصوتية"""
        try:
            self.audio_dir.mkdir(exist_ok=True)
            
            # إنشاء ملف README
            readme_file = self.audio_dir / "README.txt"
            if not readme_file.exists():
                with open(readme_file, 'w', encoding='utf-8') as f:
                    f.write("""مجلد الملفات الصوتية للأذان

ضع ملفات الأذان هنا:
- azan_fajr.mp3: أذان الفجر
- azan_normal.mp3: أذان باقي الصلوات

تنسيقات مدعومة: MP3, WAV, OGG
""")
            
        except Exception as e:
            print(f"خطأ في إنشاء مجلد الصوت: {e}")
