// Weather Service using OpenWeatherMap API
const API_KEY = 'your_openweather_api_key'; // Replace with actual API key

export const getWeatherData = async (latitude, longitude) => {
  try {
    // For demo purposes, return mock data if no API key
    if (!API_KEY || API_KEY === 'your_openweather_api_key') {
      return getMockWeatherData();
    }
    
    const response = await fetch(
      `https://api.openweathermap.org/data/2.5/weather?lat=${latitude}&lon=${longitude}&appid=${API_KEY}&units=metric&lang=ar`
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch weather data');
    }
    
    const data = await response.json();
    
    return {
      temperature: Math.round(data.main.temp),
      feelsLike: Math.round(data.main.feels_like),
      humidity: data.main.humidity,
      condition: data.weather[0].description,
      icon: data.weather[0].icon,
      city: data.name,
      country: data.sys.country
    };
  } catch (error) {
    console.error('Error fetching weather data:', error);
    return getMockWeatherData();
  }
};

const getMockWeatherData = () => {
  return {
    temperature: 30,
    feelsLike: 28,
    humidity: 41,
    condition: 'clear sky',
    icon: '01d',
    city: 'الجزائر',
    country: 'DZ'
  };
};

// Alternative weather service using a free API
export const getWeatherDataFree = async (latitude, longitude) => {
  try {
    // Using wttr.in service which doesn't require API key
    const response = await fetch(
      `https://wttr.in/${latitude},${longitude}?format=j1`
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch weather data');
    }
    
    const data = await response.json();
    const current = data.current_condition[0];
    
    return {
      temperature: parseInt(current.temp_C),
      feelsLike: parseInt(current.FeelsLikeC),
      humidity: parseInt(current.humidity),
      condition: current.weatherDesc[0].value,
      icon: getWeatherIcon(current.weatherCode),
      city: data.nearest_area[0].areaName[0].value,
      country: data.nearest_area[0].country[0].value
    };
  } catch (error) {
    console.error('Error fetching weather data:', error);
    return getMockWeatherData();
  }
};

const getWeatherIcon = (weatherCode) => {
  const code = parseInt(weatherCode);
  
  if (code === 113) return '☀️'; // Clear/Sunny
  if (code >= 116 && code <= 119) return '⛅'; // Partly cloudy
  if (code >= 122 && code <= 143) return '☁️'; // Cloudy/Overcast
  if (code >= 176 && code <= 200) return '🌧️'; // Rain
  if (code >= 227 && code <= 230) return '❄️'; // Snow
  if (code >= 248 && code <= 260) return '🌫️'; // Fog/Mist
  if (code >= 263 && code <= 284) return '🌦️'; // Light rain
  if (code >= 293 && code <= 299) return '🌧️'; // Heavy rain
  if (code >= 314 && code <= 320) return '⛈️'; // Thunderstorm
  
  return '☀️'; // Default
};
