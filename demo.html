<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شاشة أوقات الصلاة - المسجد</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Amiri', sans-serif;
            direction: rtl;
            text-align: right;
            overflow: hidden;
            background: linear-gradient(135deg, #8B0000, #DC143C, #B22222);
            color: white;
            height: 100vh;
            width: 100vw;
        }

        .app {
            height: 100vh;
            width: 100vw;
            position: relative;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
            background-size: 400% 400%;
            animation: gradientShift 10s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .decorative-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 1px, transparent 1px),
                radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 2px, transparent 2px);
            background-size: 50px 50px, 60px 60px, 80px 80px;
            pointer-events: none;
        }

        .main-container {
            position: relative;
            z-index: 1;
            height: 100vh;
            display: grid;
            grid-template-rows: auto 1fr auto;
            padding: 20px;
            gap: 20px;
        }

        .header-section {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            align-items: center;
            gap: 20px;
        }

        .weather-info {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.2rem;
        }

        .date-info {
            text-align: center;
            font-size: 1.1rem;
            line-height: 1.4;
        }

        .mosque-name {
            text-align: left;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .main-content {
            display: grid;
            grid-template-columns: 200px 1fr 200px;
            align-items: center;
            gap: 40px;
            height: 100%;
        }

        .center-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .countdown-circle {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 3px solid rgba(255,255,255,0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .current-time {
            font-size: 4rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 10px;
        }

        .prayer-times-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 15px;
            text-align: center;
            margin-top: 20px;
            width: 100%;
            max-width: 800px;
        }

        .prayer-time-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px 10px;
            border: 2px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        }

        .prayer-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .prayer-time {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .iqama-time {
            font-size: 1rem;
            opacity: 0.9;
        }

        .footer-section {
            text-align: center;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .islamic-text {
            font-size: 1.2rem;
            line-height: 1.6;
            font-family: 'Amiri', serif;
        }

        .glow {
            text-shadow: 0 0 10px rgba(255,255,255,0.5);
        }

        .admin-hint {
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="app">
        <div class="decorative-pattern"></div>
        
        <div class="main-container">
            <div class="header-section">
                <div class="weather-info">
                    <span>🇩🇿</span>
                    <div>
                        <div>☀️ 30°</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">41° 28°</div>
                    </div>
                </div>
                
                <div class="date-info">
                    <div id="gregorian-date">الخميس 5 يونيو 2025</div>
                    <div id="hijri-date">5 ذو الحجة 1446 هـ</div>
                </div>
                
                <div class="mosque-name">
                    اسم المسجد
                </div>
            </div>
            
            <div class="main-content">
                <div class="countdown-circle">
                    <div style="text-align: center;">
                        <div style="font-size: 3rem; margin-bottom: 10px;">🕌</div>
                        <div style="font-size: 1rem;">المسجد</div>
                    </div>
                </div>
                
                <div class="center-section">
                    <div style="text-align: center;">
                        <div class="current-time glow" id="current-time">2:13</div>
                        <div style="font-size: 1.2rem; opacity: 0.9;" id="time-period">صباحاً</div>
                    </div>
                    
                    <div class="prayer-times-grid">
                        <div class="prayer-time-card">
                            <div class="prayer-name">الفجر</div>
                            <div class="prayer-time">4:11</div>
                            <div class="iqama-time">4:21</div>
                        </div>
                        <div class="prayer-time-card">
                            <div class="prayer-name">الشروق</div>
                            <div class="prayer-time">5:37</div>
                            <div class="iqama-time">5:52</div>
                        </div>
                        <div class="prayer-time-card">
                            <div class="prayer-name">الظهر</div>
                            <div class="prayer-time">12:20</div>
                            <div class="iqama-time">12:30</div>
                        </div>
                        <div class="prayer-time-card">
                            <div class="prayer-name">العصر</div>
                            <div class="prayer-time">3:37</div>
                            <div class="iqama-time">3:47</div>
                        </div>
                        <div class="prayer-time-card">
                            <div class="prayer-name">المغرب</div>
                            <div class="prayer-time">7:01</div>
                            <div class="iqama-time">7:11</div>
                        </div>
                        <div class="prayer-time-card">
                            <div class="prayer-name">العشاء</div>
                            <div class="prayer-time">8:31</div>
                            <div class="iqama-time">8:41</div>
                        </div>
                    </div>
                </div>
                
                <div class="countdown-circle">
                    <div style="text-align: center;">
                        <div style="font-size: 1rem; margin-bottom: 10px;">الفجر</div>
                        <div style="font-size: 1.5rem; font-weight: bold;">1:58</div>
                    </div>
                </div>
            </div>
            
            <div class="footer-section">
                <div class="islamic-text" id="islamic-text">
                    وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا
                </div>
            </div>
        </div>
        
        <div class="admin-hint">
            اضغط 'A' لفتح لوحة التحكم
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            
            const hour = now.getHours();
            let period = 'صباحاً';
            if (hour >= 5 && hour < 12) period = 'صباحاً';
            else if (hour >= 12 && hour < 17) period = 'ظهراً';
            else if (hour >= 17 && hour < 20) period = 'عصراً';
            else if (hour >= 20 && hour < 24) period = 'مساءً';
            else period = 'ليلاً';
            
            document.getElementById('current-time').textContent = timeStr;
            document.getElementById('time-period').textContent = period;
        }

        // Update dates
        function updateDates() {
            const now = new Date();
            const gregorianDate = now.toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            document.getElementById('gregorian-date').textContent = gregorianDate;
        }

        // Rotate Islamic texts
        const islamicTexts = [
            "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
            "إِنَّ مَعَ الْعُسْرِ يُسْرًا",
            "وَاللَّهُ خَيْرٌ حَافِظًا وَهُوَ أَرْحَمُ الرَّاحِمِينَ",
            "قال رسول الله ﷺ: الصلاة نور",
            "قال رسول الله ﷺ: خير الناس أنفعهم للناس",
            "وَبَشِّرِ الصَّابِرِينَ"
        ];
        
        let currentTextIndex = 0;
        
        function rotateText() {
            const textElement = document.getElementById('islamic-text');
            textElement.style.opacity = '0';
            
            setTimeout(() => {
                textElement.textContent = islamicTexts[currentTextIndex];
                textElement.style.opacity = '1';
                currentTextIndex = (currentTextIndex + 1) % islamicTexts.length;
            }, 500);
        }

        // Initialize
        updateTime();
        updateDates();
        
        // Set intervals
        setInterval(updateTime, 1000);
        setInterval(rotateText, 10000);
        
        // Admin panel simulation
        document.addEventListener('keydown', function(event) {
            if (event.key === 'a' || event.key === 'A') {
                alert('لوحة التحكم الإدارية\n\nهذه نسخة تجريبية من البرنامج.\nفي النسخة الكاملة، ستتمكن من:\n\n• تعديل أوقات الصلاة\n• تخصيص أصوات الأذان\n• إدارة النصوص الإسلامية\n• تعديل إعدادات العرض');
            }
        });
    </script>
</body>
</html>
