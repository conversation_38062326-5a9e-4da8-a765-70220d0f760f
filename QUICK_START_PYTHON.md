# دليل التشغيل السريع - نسخة Python
## Quick Start Guide - Python Version

## 🚀 التشغيل الفوري (5 دقائق)

### الخطوة 1: تحقق من Python
```bash
python --version
# يجب أن يكون 3.8 أو أحدث
```

### الخطوة 2: تثبيت المتطلبات الأساسية
```bash
# للنسخة المبسطة (فقط tkinter مطلوب)
python run_simple.py

# أو تثبيت جميع المتطلبات للنسخة الكاملة
pip install requests pygame Pillow python-bidi arabic-reshaper hijri-converter geopy pyttsx3 schedule
```

### الخطوة 3: تشغيل البرنامج
```bash
# النسخة المبسطة (موصى بها للبداية)
python run_simple.py

# أو النسخة الكاملة
python main.py
```

## ✨ الميزات المتوفرة فوراً

### ✅ النسخة المبسطة (`run_simple.py`)
- [x] واجهة رسومية بالتصميم الأحمر المطلوب
- [x] ساعة رقمية كبيرة مع تحديث مباشر
- [x] عرض التاريخ الهجري والميلادي
- [x] أوقات الصلاة الستة مع أوقات الإقامة
- [x] عد تنازلي للصلاة القادمة
- [x] معلومات الطقس الأساسية
- [x] النصوص الإسلامية المتناوبة
- [x] دعم اللغة العربية والاتجاه RTL
- [x] اختصارات لوحة المفاتيح

### ✅ النسخة الكاملة (`main.py`)
- [x] جميع ميزات النسخة المبسطة
- [x] حساب أوقات الصلاة من API حقيقي
- [x] نظام الأذان الصوتي التلقائي
- [x] لوحة التحكم الإدارية الكاملة
- [x] معلومات الطقس الحية
- [x] حفظ واستعادة الإعدادات
- [x] دعم ملفات الأذان الصوتية

## 🎮 الاستخدام السريع

### اختصارات لوحة المفاتيح
| المفتاح | الوظيفة |
|---------|---------|
| `A` | فتح لوحة التحكم الإدارية |
| `Escape` | إغلاق لوحة التحكم / تبديل ملء الشاشة |
| `F11` | تبديل وضع ملء الشاشة |
| `Alt+F4` | إغلاق البرنامج |

### التخصيص السريع

#### تغيير أوقات الصلاة
1. اضغط `A` لفتح لوحة التحكم
2. انتقل إلى تبويب "أوقات الصلاة"
3. عدّل الأوقات حسب الحاجة
4. اضغط "حفظ الإعدادات"

#### إضافة نصوص إسلامية
1. اضغط `A` لفتح لوحة التحكم
2. انتقل إلى تبويب "النصوص الإسلامية"
3. اكتب النص الجديد
4. اضغط "إضافة النص"

#### تعديل اسم المسجد
1. اضغط `A` لفتح لوحة التحكم
2. انتقل إلى تبويب "الإعدادات العامة"
3. غيّر اسم المسجد
4. اضغط "حفظ الإعدادات"

## 📁 الملفات المهمة

```
📂 المشروع/
├── 🐍 run_simple.py          # النسخة المبسطة - ابدأ هنا!
├── 🐍 main.py               # النسخة الكاملة
├── 🔧 build_exe.py          # بناء ملف .exe
├── 📋 requirements.txt      # قائمة المكتبات
├── 🔨 install_requirements.bat # تثبيت تلقائي (Windows)
├── 📖 README_PYTHON.md      # التوثيق الكامل
├── 📂 src/                  # الكود المصدري
├── 📂 audio/                # ملفات الأذان (اختياري)
└── 📂 config/               # ملفات الإعدادات
```

## 🔧 حل المشاكل السريع

### المشكلة: "python غير معروف"
**الحل:**
```bash
# تحقق من تثبيت Python
where python
# أو
python3 --version
```

### المشكلة: "ModuleNotFoundError"
**الحل:**
```bash
# تثبيت المكتبة المفقودة
pip install [اسم المكتبة]

# أو تثبيت جميع المتطلبات
pip install -r requirements.txt
```

### المشكلة: النصوص العربية لا تظهر
**الحل:**
```bash
pip install arabic-reshaper python-bidi
```

### المشكلة: الأذان لا يعمل
**الحل:**
1. تأكد من وجود ملفات الأذان في مجلد `audio/`
2. جرب النسخة المبسطة أولاً
3. تحقق من مستوى الصوت

## 🏗️ بناء ملف تنفيذي

### للحصول على ملف .exe
```bash
# تثبيت PyInstaller
pip install pyinstaller

# بناء التطبيق
python build_exe.py

# اختر النوع المطلوب:
# 1 = النسخة المبسطة (أصغر حجماً)
# 2 = النسخة الكاملة (جميع الميزات)
# 3 = كلاهما

# الملف التنفيذي سيكون في مجلد dist/
```

## 📱 للاستخدام على شاشة كبيرة

### إعدادات مُوصى بها
- **دقة الشاشة**: 1920x1080 أو أعلى
- **وضع العرض**: ملء الشاشة (F11)
- **الخط**: يتكيف تلقائياً مع حجم الشاشة
- **الألوان**: محسّنة للرؤية من مسافة بعيدة

### نصائح للعرض
1. شغّل البرنامج في وضع ملء الشاشة (F11)
2. تأكد من وضوح الشاشة وسطوعها
3. اختبر الرؤية من مسافات مختلفة
4. استخدم شاشة LED أو LCD عالية الجودة

## 🕌 للاستخدام في المسجد

### قبل التشغيل
- [ ] تحديد الموقع الجغرافي الصحيح
- [ ] تعديل أوقات الصلاة حسب المسجد
- [ ] إضافة اسم المسجد
- [ ] اختبار نظام الأذان
- [ ] تحضير ملفات الأذان الصوتية

### أثناء التشغيل
- [ ] مراقبة دقة الأوقات
- [ ] التأكد من عمل الأذان التلقائي
- [ ] تحديث النصوص الإسلامية حسب الحاجة
- [ ] مراجعة الإعدادات دورياً

### صيانة دورية
- [ ] تحديث أوقات الصلاة شهرياً
- [ ] فحص اتصال الإنترنت
- [ ] نسخ احتياطي للإعدادات
- [ ] تنظيف الشاشة والجهاز

## 📞 الدعم السريع

### للمساعدة الفورية
1. **راجع الأخطاء**: تحقق من نافذة console للأخطاء
2. **جرب النسخة المبسطة**: إذا فشلت النسخة الكاملة
3. **أعد تثبيت المتطلبات**: `pip install -r requirements.txt --force-reinstall`
4. **راجع التوثيق الكامل**: `README_PYTHON.md`

### معلومات مفيدة للدعم
```bash
# معلومات النظام
python --version
pip list
```

## 🎯 الخطوات التالية

بعد التشغيل الناجح:

1. **اقرأ التوثيق الكامل**: `README_PYTHON.md`
2. **خصص الإعدادات**: حسب احتياجات المسجد
3. **أضف ملفات الأذان**: للحصول على تجربة كاملة
4. **اختبر جميع الميزات**: قبل الاستخدام الفعلي
5. **انشر البرنامج**: في المساجد الأخرى

---

**🕌 بارك الله فيكم على استخدام هذا البرنامج في خدمة دين الله**

**للمساعدة الإضافية، راجع `README_PYTHON.md` للتفاصيل الكاملة**
