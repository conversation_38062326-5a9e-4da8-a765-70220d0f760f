#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات معالجة النصوص العربية
"""

import arabic_reshaper
from bidi.algorithm import get_display
from hijri_converter import Hi<PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime

class ArabicTextHandler:
    """معالج النصوص العربية"""
    
    def __init__(self):
        self.reshaper_config = {
            'delete_harakat': False,
            'support_zwj': True,
            'shift_harakat_position': False,
        }
    
    def format_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            if not text:
                return ""
            
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(
                text, 
                configuration=self.reshaper_config
            )
            
            # تطبيق خوارزمية الاتجاه الثنائي
            display_text = get_display(reshaped_text)
            
            return display_text
            
        except Exception as e:
            print(f"خطأ في تنسيق النص العربي: {e}")
            return text
    
    def format_prayer_name(self, prayer_key):
        """تنسيق اسم الصلاة"""
        prayer_names = {
            'fajr': 'الفجر',
            'sunrise': 'الشروق',
            'dhuhr': 'الظهر',
            'asr': 'العصر',
            'maghrib': 'المغرب',
            'isha': 'العشاء'
        }
        
        name = prayer_names.get(prayer_key, prayer_key)
        return self.format_text(name)
    
    def format_day_name(self, day_name):
        """تنسيق اسم اليوم"""
        day_names = {
            'Monday': 'الاثنين',
            'Tuesday': 'الثلاثاء',
            'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس',
            'Friday': 'الجمعة',
            'Saturday': 'السبت',
            'Sunday': 'الأحد'
        }
        
        arabic_name = day_names.get(day_name, day_name)
        return self.format_text(arabic_name)
    
    def format_month_name(self, month_name):
        """تنسيق اسم الشهر"""
        month_names = {
            'January': 'يناير',
            'February': 'فبراير',
            'March': 'مارس',
            'April': 'أبريل',
            'May': 'مايو',
            'June': 'يونيو',
            'July': 'يوليو',
            'August': 'أغسطس',
            'September': 'سبتمبر',
            'October': 'أكتوبر',
            'November': 'نوفمبر',
            'December': 'ديسمبر'
        }
        
        arabic_name = month_names.get(month_name, month_name)
        return self.format_text(arabic_name)
    
    def format_hijri_month(self, month_number):
        """تنسيق اسم الشهر الهجري"""
        hijri_months = [
            'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني',
            'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان',
            'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
        ]
        
        if 1 <= month_number <= 12:
            month_name = hijri_months[month_number - 1]
            return self.format_text(month_name)
        
        return str(month_number)
    
    def get_gregorian_date(self, date=None):
        """الحصول على التاريخ الميلادي بالعربية"""
        if date is None:
            date = datetime.now()
        
        try:
            # اسم اليوم
            day_name = date.strftime('%A')
            arabic_day = self.format_day_name(day_name)
            
            # رقم اليوم
            day_number = date.day
            
            # اسم الشهر
            month_name = date.strftime('%B')
            arabic_month = self.format_month_name(month_name)
            
            # السنة
            year = date.year
            
            # تكوين التاريخ الكامل
            full_date = f"{arabic_day} {day_number} {arabic_month} {year}"
            return self.format_text(full_date)
            
        except Exception as e:
            print(f"خطأ في تنسيق التاريخ الميلادي: {e}")
            return date.strftime('%Y-%m-%d')
    
    def get_hijri_date(self, date=None):
        """الحصول على التاريخ الهجري بالعربية"""
        if date is None:
            date = datetime.now()
        
        try:
            # تحويل إلى التاريخ الهجري
            gregorian_date = Gregorian(date.year, date.month, date.day)
            hijri_date = gregorian_date.to_hijri()
            
            # تنسيق التاريخ الهجري
            day = hijri_date.day
            month_name = self.format_hijri_month(hijri_date.month)
            year = hijri_date.year
            
            full_hijri_date = f"{day} {month_name} {year} هـ"
            return self.format_text(full_hijri_date)
            
        except Exception as e:
            print(f"خطأ في تحويل التاريخ الهجري: {e}")
            # تاريخ هجري تقريبي
            return self.format_text("5 ذو الحجة 1446 هـ")
    
    def get_time_period(self, hour):
        """الحصول على فترة اليوم بالعربية"""
        if 5 <= hour < 12:
            period = "صباحاً"
        elif 12 <= hour < 17:
            period = "ظهراً"
        elif 17 <= hour < 20:
            period = "عصراً"
        elif 20 <= hour < 24:
            period = "مساءً"
        else:
            period = "ليلاً"
        
        return self.format_text(period)
    
    def format_time_remaining(self, hours, minutes):
        """تنسيق الوقت المتبقي"""
        if hours > 0:
            if hours == 1:
                time_text = f"ساعة و {minutes} دقيقة"
            else:
                time_text = f"{hours} ساعات و {minutes} دقيقة"
        else:
            if minutes == 1:
                time_text = "دقيقة واحدة"
            elif minutes == 2:
                time_text = "دقيقتان"
            elif 3 <= minutes <= 10:
                time_text = f"{minutes} دقائق"
            else:
                time_text = f"{minutes} دقيقة"
        
        return self.format_text(time_text)
    
    def validate_arabic_text(self, text):
        """التحقق من صحة النص العربي"""
        if not text:
            return False
        
        # التحقق من وجود أحرف عربية
        arabic_chars = set('ابتثجحخدذرزسشصضطظعغفقكلمنهوي')
        text_chars = set(text.replace(' ', '').replace('\n', ''))
        
        return bool(arabic_chars.intersection(text_chars))
    
    def clean_text(self, text):
        """تنظيف النص من الأحرف غير المرغوبة"""
        if not text:
            return ""
        
        # إزالة الأحرف الخاصة غير المرغوبة
        unwanted_chars = ['\u200c', '\u200d', '\u200e', '\u200f']
        
        for char in unwanted_chars:
            text = text.replace(char, '')
        
        # تنظيف المسافات الزائدة
        text = ' '.join(text.split())
        
        return text.strip()
    
    def wrap_text(self, text, max_width=50):
        """تقسيم النص الطويل إلى أسطر"""
        if not text or len(text) <= max_width:
            return [text]
        
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            if len(current_line + " " + word) <= max_width:
                if current_line:
                    current_line += " " + word
                else:
                    current_line = word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
