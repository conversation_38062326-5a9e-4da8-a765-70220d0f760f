// Date utilities for Hijri and Gregorian dates

export const formatGregorianDate = (date) => {
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  };
  
  return date.toLocaleDateString('ar-SA', options);
};

export const formatHijriDate = (date) => {
  // Simple Hijri date calculation (approximate)
  // For production, use a proper Hijri calendar library
  
  const hijriMonths = [
    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني',
    'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان',
    'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
  ];
  
  // Approximate conversion (this is not accurate for production use)
  const gregorianYear = date.getFullYear();
  const hijriYear = Math.floor((gregorianYear - 622) * 1.030684) + 1;
  
  const dayOfYear = Math.floor((date - new Date(gregorianYear, 0, 0)) / 86400000);
  const hijriMonth = Math.floor(dayOfYear / 29.5) % 12;
  const hijriDay = Math.floor(dayOfYear % 29.5) + 1;
  
  return `${hijriDay} ${hijriMonths[hijriMonth]} ${hijriYear} هـ`;
};

export const getHijriDateAccurate = (date) => {
  // This would use a proper Hijri calendar library in production
  // For now, return a sample date
  return "5 ذو الحجة 1446 هـ";
};

export const formatTime12Hour = (date) => {
  return date.toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

export const formatTime24Hour = (date) => {
  return date.toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

export const getTimeUntilNextPrayer = (currentTime, nextPrayerTime) => {
  const [hours, minutes] = nextPrayerTime.split(':').map(Number);
  const nextPrayer = new Date(currentTime);
  nextPrayer.setHours(hours, minutes, 0, 0);
  
  if (nextPrayer <= currentTime) {
    nextPrayer.setDate(nextPrayer.getDate() + 1);
  }
  
  const diff = nextPrayer - currentTime;
  const hoursLeft = Math.floor(diff / (1000 * 60 * 60));
  const minutesLeft = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const secondsLeft = Math.floor((diff % (1000 * 60)) / 1000);
  
  return {
    hours: hoursLeft,
    minutes: minutesLeft,
    seconds: secondsLeft,
    totalSeconds: Math.floor(diff / 1000)
  };
};
