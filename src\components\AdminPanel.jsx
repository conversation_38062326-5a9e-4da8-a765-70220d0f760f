import React, { useState, useEffect } from 'react';
import azanService from '../services/azanService';
import './AdminPanel.css';

const AdminPanel = ({ isOpen, onClose, prayerTimes, onUpdatePrayerTimes }) => {
  const [settings, setSettings] = useState({
    mosqueName: 'اسم المسجد',
    volume: 0.8,
    prayerTimes: prayerTimes || {},
    islamicTexts: [
      "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
      "إِنَّ مَعَ الْعُسْرِ يُسْرًا",
      "وَاللَّهُ خَيْرٌ حَافِظًا وَهُوَ أَرْحَمُ الرَّاحِمِينَ"
    ],
    newText: ''
  });

  const [activeTab, setActiveTab] = useState('times');

  useEffect(() => {
    if (prayerTimes) {
      setSettings(prev => ({ ...prev, prayerTimes }));
    }
  }, [prayerTimes]);

  const handleTimeChange = (prayer, type, value) => {
    setSettings(prev => ({
      ...prev,
      prayerTimes: {
        ...prev.prayerTimes,
        [prayer]: {
          ...prev.prayerTimes[prayer],
          [type]: value
        }
      }
    }));
  };

  const handleVolumeChange = (value) => {
    const volume = parseFloat(value);
    setSettings(prev => ({ ...prev, volume }));
    azanService.setVolume(volume);
  };

  const handleSaveSettings = () => {
    // Save to localStorage
    localStorage.setItem('mosqueSettings', JSON.stringify(settings));
    
    // Update prayer times
    if (onUpdatePrayerTimes) {
      onUpdatePrayerTimes(settings.prayerTimes);
    }
    
    // Reschedule azans
    azanService.scheduleAzan(settings.prayerTimes);
    
    alert('تم حفظ الإعدادات بنجاح');
  };

  const handleTestAzan = (prayer) => {
    azanService.playAzan(prayer);
  };

  const handleAddText = () => {
    if (settings.newText.trim()) {
      setSettings(prev => ({
        ...prev,
        islamicTexts: [...prev.islamicTexts, settings.newText.trim()],
        newText: ''
      }));
    }
  };

  const handleRemoveText = (index) => {
    setSettings(prev => ({
      ...prev,
      islamicTexts: prev.islamicTexts.filter((_, i) => i !== index)
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="admin-overlay">
      <div className="admin-panel">
        <div className="admin-header">
          <h2>لوحة التحكم الإدارية</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="admin-tabs">
          <button 
            className={activeTab === 'times' ? 'active' : ''}
            onClick={() => setActiveTab('times')}
          >
            أوقات الصلاة
          </button>
          <button 
            className={activeTab === 'audio' ? 'active' : ''}
            onClick={() => setActiveTab('audio')}
          >
            إعدادات الصوت
          </button>
          <button 
            className={activeTab === 'texts' ? 'active' : ''}
            onClick={() => setActiveTab('texts')}
          >
            النصوص الإسلامية
          </button>
          <button 
            className={activeTab === 'general' ? 'active' : ''}
            onClick={() => setActiveTab('general')}
          >
            إعدادات عامة
          </button>
        </div>

        <div className="admin-content">
          {activeTab === 'times' && (
            <div className="times-section">
              <h3>تعديل أوقات الصلاة والإقامة</h3>
              {Object.entries(settings.prayerTimes).map(([prayer, times]) => (
                <div key={prayer} className="prayer-row">
                  <label>{getPrayerName(prayer)}</label>
                  <div className="time-inputs">
                    <div>
                      <span>الأذان:</span>
                      <input
                        type="time"
                        value={times.azan || ''}
                        onChange={(e) => handleTimeChange(prayer, 'azan', e.target.value)}
                      />
                    </div>
                    <div>
                      <span>الإقامة:</span>
                      <input
                        type="time"
                        value={times.iqama || ''}
                        onChange={(e) => handleTimeChange(prayer, 'iqama', e.target.value)}
                      />
                    </div>
                    <button 
                      className="test-btn"
                      onClick={() => handleTestAzan(prayer)}
                    >
                      تجربة الأذان
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'audio' && (
            <div className="audio-section">
              <h3>إعدادات الصوت</h3>
              <div className="volume-control">
                <label>مستوى الصوت:</label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={settings.volume}
                  onChange={(e) => handleVolumeChange(e.target.value)}
                />
                <span>{Math.round(settings.volume * 100)}%</span>
              </div>
              <div className="audio-test">
                <button onClick={() => handleTestAzan('dhuhr')}>
                  تجربة الأذان
                </button>
                <button onClick={() => azanService.stopAzan()}>
                  إيقاف الأذان
                </button>
              </div>
            </div>
          )}

          {activeTab === 'texts' && (
            <div className="texts-section">
              <h3>إدارة النصوص الإسلامية</h3>
              <div className="add-text">
                <textarea
                  value={settings.newText}
                  onChange={(e) => setSettings(prev => ({ ...prev, newText: e.target.value }))}
                  placeholder="أدخل نص قرآني أو حديث شريف..."
                  rows="3"
                />
                <button onClick={handleAddText}>إضافة نص</button>
              </div>
              <div className="texts-list">
                {settings.islamicTexts.map((text, index) => (
                  <div key={index} className="text-item">
                    <span>{text}</span>
                    <button onClick={() => handleRemoveText(index)}>حذف</button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'general' && (
            <div className="general-section">
              <h3>الإعدادات العامة</h3>
              <div className="setting-row">
                <label>اسم المسجد:</label>
                <input
                  type="text"
                  value={settings.mosqueName}
                  onChange={(e) => setSettings(prev => ({ ...prev, mosqueName: e.target.value }))}
                />
              </div>
            </div>
          )}
        </div>

        <div className="admin-footer">
          <button className="save-btn" onClick={handleSaveSettings}>
            حفظ الإعدادات
          </button>
          <button className="cancel-btn" onClick={onClose}>
            إلغاء
          </button>
        </div>
      </div>
    </div>
  );
};

const getPrayerName = (prayer) => {
  const names = {
    fajr: 'الفجر',
    sunrise: 'الشروق',
    dhuhr: 'الظهر',
    asr: 'العصر',
    maghrib: 'المغرب',
    isha: 'العشاء'
  };
  return names[prayer] || prayer;
};

export default AdminPanel;
