import React from 'react';

const TimeDisplay = ({ currentTime }) => {
  const formatTime = (date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatPeriod = (date) => {
    const hour = date.getHours();
    if (hour >= 5 && hour < 12) return 'صباحاً';
    if (hour >= 12 && hour < 17) return 'ظهراً';
    if (hour >= 17 && hour < 20) return 'عصراً';
    if (hour >= 20 && hour < 24) return 'مساءً';
    return 'ليلاً';
  };

  return (
    <div className="time-display">
      <div className="current-time glow">
        {formatTime(currentTime)}
      </div>
      <div style={{ fontSize: '1.2rem', opacity: 0.9 }}>
        {formatPeriod(currentTime)}
      </div>
    </div>
  );
};

export default TimeDisplay;
