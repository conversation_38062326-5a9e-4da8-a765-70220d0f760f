import React from 'react';

const PrayerTimes = ({ prayerTimes }) => {
  const defaultTimes = {
    fajr: { azan: '4:11', iqama: '4:21' },
    sunrise: { azan: '5:37', iqama: '5:52' },
    dhuhr: { azan: '12:20', iqama: '12:30' },
    asr: { azan: '3:37', iqama: '3:47' },
    maghrib: { azan: '7:01', iqama: '7:11' },
    isha: { azan: '8:31', iqama: '8:41' }
  };

  const prayers = [
    { name: 'الفجر', key: 'fajr' },
    { name: 'الشروق', key: 'sunrise' },
    { name: 'الظهر', key: 'dhuhr' },
    { name: 'العصر', key: 'asr' },
    { name: 'المغرب', key: 'maghrib' },
    { name: 'العشاء', key: 'isha' }
  ];

  const times = prayerTimes || defaultTimes;

  return (
    <div className="prayer-times-grid">
      {prayers.map((prayer) => (
        <div key={prayer.key} className="prayer-time-card">
          <div className="prayer-name">{prayer.name}</div>
          <div className="prayer-time">{times[prayer.key]?.azan || '00:00'}</div>
          <div className="iqama-time">{times[prayer.key]?.iqama || '00:00'}</div>
        </div>
      ))}
    </div>
  );
};

export default PrayerTimes;
