#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط لبرنامج شاشة أوقات الصلاة
نسخة مبسطة للاختبار بدون مكتبات معقدة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, timedelta
import json
import os

class SimpleMosqueApp:
    """نسخة مبسطة من برنامج شاشة أوقات الصلاة"""
    
    def __init__(self, root):
        self.root = root
        self.current_time = datetime.now()
        self.current_text_index = 0
        self.is_running = True
        
        # البيانات الأساسية
        self.prayer_times = {
            'fajr': {'azan': '04:11', 'iqama': '04:21'},
            'sunrise': {'azan': '05:37', 'iqama': '05:52'},
            'dhuhr': {'azan': '12:20', 'iqama': '12:30'},
            'asr': {'azan': '15:37', 'iqama': '15:47'},
            'maghrib': {'azan': '19:01', 'iqama': '19:11'},
            'isha': {'azan': '20:31', 'iqama': '20:41'}
        }
        
        self.islamic_texts = [
            "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
            "إِنَّ مَعَ الْعُسْرِ يُسْرًا",
            "وَاللَّهُ خَيْرٌ حَافِظًا وَهُوَ أَرْحَمُ الرَّاحِمِينَ",
            "قال رسول الله ﷺ: الصلاة نور",
            "قال رسول الله ﷺ: خير الناس أنفعهم للناس",
            "وَبَشِّرِ الصَّابِرِينَ"
        ]
        
        self.setup_ui()
        self.start_updates()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.root.title("شاشة أوقات الصلاة - المسجد")
        self.root.configure(bg='#8B0000')
        self.root.state('zoomed')  # ملء الشاشة
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#8B0000')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # رأس الصفحة
        self.create_header(main_frame)
        
        # المحتوى الرئيسي
        self.create_main_content(main_frame)
        
        # التذييل
        self.create_footer(main_frame)
        
        # ربط الأحداث
        self.root.bind('<Key-a>', self.show_admin_info)
        self.root.bind('<Key-A>', self.show_admin_info)
        self.root.bind('<Escape>', self.toggle_fullscreen)
        self.root.bind('<F11>', self.toggle_fullscreen)
        self.root.focus_set()
    
    def create_header(self, parent):
        """إنشاء رأس الصفحة"""
        header_frame = tk.Frame(parent, bg='#8B0000')
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # تقسيم الرأس إلى ثلاثة أقسام
        header_frame.grid_columnconfigure(0, weight=1)
        header_frame.grid_columnconfigure(1, weight=1)
        header_frame.grid_columnconfigure(2, weight=1)
        
        # الطقس (يسار)
        weather_frame = tk.Frame(header_frame, bg='#8B0000')
        weather_frame.grid(row=0, column=0, sticky='w')
        
        flag_label = tk.Label(weather_frame, text="🇩🇿", font=('Arial', 24), bg='#8B0000', fg='white')
        flag_label.pack(side=tk.LEFT, padx=(0, 10))
        
        temp_label = tk.Label(weather_frame, text="☀️ 30°", font=('Arial', 16), bg='#8B0000', fg='white')
        temp_label.pack(side=tk.LEFT)
        
        # التاريخ (وسط)
        date_frame = tk.Frame(header_frame, bg='#8B0000')
        date_frame.grid(row=0, column=1)
        
        self.gregorian_date_label = tk.Label(
            date_frame, 
            text="", 
            font=('Arial', 16), 
            bg='#8B0000', 
            fg='white'
        )
        self.gregorian_date_label.pack()
        
        self.hijri_date_label = tk.Label(
            date_frame, 
            text="5 ذو الحجة 1446 هـ", 
            font=('Arial', 16), 
            bg='#8B0000', 
            fg='white'
        )
        self.hijri_date_label.pack()
        
        # اسم المسجد (يمين)
        mosque_label = tk.Label(
            header_frame, 
            text="اسم المسجد", 
            font=('Arial', 20, 'bold'), 
            bg='#8B0000', 
            fg='white'
        )
        mosque_label.grid(row=0, column=2, sticky='e')
    
    def create_main_content(self, parent):
        """إنشاء المحتوى الرئيسي"""
        content_frame = tk.Frame(parent, bg='#8B0000')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # تقسيم إلى ثلاثة أعمدة
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_columnconfigure(1, weight=3)
        content_frame.grid_columnconfigure(2, weight=1)
        
        # الدائرة اليسرى (المسجد)
        left_frame = tk.Frame(content_frame, bg='#8B0000')
        left_frame.grid(row=0, column=0, sticky='nsew')
        
        mosque_circle = tk.Label(
            left_frame, 
            text="🕌\nالمسجد", 
            font=('Arial', 24), 
            bg='#B22222', 
            fg='white',
            width=10,
            height=8,
            relief=tk.RAISED,
            bd=3
        )
        mosque_circle.pack(expand=True)
        
        # المنتصف (الوقت وأوقات الصلاة)
        center_frame = tk.Frame(content_frame, bg='#8B0000')
        center_frame.grid(row=0, column=1, sticky='nsew', padx=40)
        
        # الساعة
        self.time_label = tk.Label(
            center_frame, 
            text="", 
            font=('Arial', 72, 'bold'), 
            bg='#8B0000', 
            fg='white'
        )
        self.time_label.pack(pady=(0, 10))
        
        self.period_label = tk.Label(
            center_frame, 
            text="", 
            font=('Arial', 18), 
            bg='#8B0000', 
            fg='white'
        )
        self.period_label.pack(pady=(0, 30))
        
        # أوقات الصلاة
        self.create_prayer_times_grid(center_frame)
        
        # الدائرة اليمنى (العد التنازلي)
        right_frame = tk.Frame(content_frame, bg='#8B0000')
        right_frame.grid(row=0, column=2, sticky='nsew')
        
        self.countdown_label = tk.Label(
            right_frame, 
            text="1:58\nالفجر", 
            font=('Arial', 20), 
            bg='#B22222', 
            fg='white',
            width=10,
            height=8,
            relief=tk.RAISED,
            bd=3
        )
        self.countdown_label.pack(expand=True)
    
    def create_prayer_times_grid(self, parent):
        """إنشاء شبكة أوقات الصلاة"""
        prayers_frame = tk.Frame(parent, bg='#8B0000')
        prayers_frame.pack(fill=tk.X)
        
        prayers = [
            ('الفجر', 'fajr'),
            ('الشروق', 'sunrise'),
            ('الظهر', 'dhuhr'),
            ('العصر', 'asr'),
            ('المغرب', 'maghrib'),
            ('العشاء', 'isha')
        ]
        
        self.prayer_labels = {}
        
        for i, (prayer_name, prayer_key) in enumerate(prayers):
            # إطار البطاقة
            card_frame = tk.Frame(prayers_frame, bg='#B22222', relief=tk.RAISED, bd=2)
            card_frame.grid(row=0, column=i, padx=5, pady=10, sticky='ew')
            
            # اسم الصلاة
            name_label = tk.Label(
                card_frame, 
                text=prayer_name, 
                font=('Arial', 14, 'bold'), 
                bg='#B22222', 
                fg='white'
            )
            name_label.pack(pady=(10, 5))
            
            # وقت الأذان
            azan_label = tk.Label(
                card_frame, 
                text=self.prayer_times[prayer_key]['azan'], 
                font=('Arial', 18, 'bold'), 
                bg='#B22222', 
                fg='white'
            )
            azan_label.pack(pady=2)
            
            # وقت الإقامة
            iqama_label = tk.Label(
                card_frame, 
                text=self.prayer_times[prayer_key]['iqama'], 
                font=('Arial', 14), 
                bg='#B22222', 
                fg='white'
            )
            iqama_label.pack(pady=(2, 10))
            
            self.prayer_labels[prayer_key] = {
                'azan': azan_label,
                'iqama': iqama_label
            }
        
        # توزيع الأعمدة
        for i in range(6):
            prayers_frame.grid_columnconfigure(i, weight=1)
    
    def create_footer(self, parent):
        """إنشاء التذييل"""
        footer_frame = tk.Frame(parent, bg='#B22222', relief=tk.RAISED, bd=3)
        footer_frame.pack(fill=tk.X, pady=10)
        
        self.islamic_text_label = tk.Label(
            footer_frame, 
            text=self.islamic_texts[0], 
            font=('Arial', 16), 
            bg='#B22222', 
            fg='white',
            wraplength=1200,
            justify=tk.CENTER
        )
        self.islamic_text_label.pack(pady=15)
    
    def start_updates(self):
        """بدء تحديث البيانات"""
        # تحديث الوقت
        self.update_time()
        
        # تحديث النصوص الإسلامية
        self.update_islamic_text()
        
        # تحديث التاريخ
        self.update_dates()
    
    def update_time(self):
        """تحديث الوقت"""
        if not self.is_running:
            return
        
        self.current_time = datetime.now()
        
        # تنسيق الوقت
        time_str = self.current_time.strftime("%H:%M")
        self.time_label.config(text=time_str)
        
        # فترة اليوم
        hour = self.current_time.hour
        if 5 <= hour < 12:
            period = "صباحاً"
        elif 12 <= hour < 17:
            period = "ظهراً"
        elif 17 <= hour < 20:
            period = "عصراً"
        elif 20 <= hour < 24:
            period = "مساءً"
        else:
            period = "ليلاً"
        
        self.period_label.config(text=period)
        
        # تحديث العد التنازلي
        self.update_countdown()
        
        # جدولة التحديث التالي
        self.root.after(1000, self.update_time)
    
    def update_countdown(self):
        """تحديث العد التنازلي"""
        next_prayer = self.find_next_prayer()
        if next_prayer:
            prayer_name, time_remaining = next_prayer
            self.countdown_label.config(text=f"{time_remaining}\n{prayer_name}")
    
    def find_next_prayer(self):
        """البحث عن الصلاة القادمة"""
        prayer_names = {
            'fajr': 'الفجر',
            'dhuhr': 'الظهر',
            'asr': 'العصر',
            'maghrib': 'المغرب',
            'isha': 'العشاء'
        }
        
        current_minutes = self.current_time.hour * 60 + self.current_time.minute
        
        # قائمة الصلوات مع أوقاتها
        prayers_today = []
        for prayer_key, times in self.prayer_times.items():
            if prayer_key == 'sunrise':  # تجاهل الشروق
                continue
                
            azan_time = times['azan']
            try:
                hour, minute = map(int, azan_time.split(':'))
                prayer_minutes = hour * 60 + minute
                prayers_today.append((prayer_key, prayer_minutes))
            except:
                continue
        
        # ترتيب الصلوات حسب الوقت
        prayers_today.sort(key=lambda x: x[1])
        
        # البحث عن الصلاة القادمة
        for prayer_key, prayer_minutes in prayers_today:
            if prayer_minutes > current_minutes:
                time_diff = prayer_minutes - current_minutes
                hours = time_diff // 60
                minutes = time_diff % 60
                
                if hours > 0:
                    time_str = f"{hours}:{minutes:02d}"
                else:
                    time_str = f"{minutes:02d}"
                
                return prayer_names[prayer_key], time_str
        
        # إذا لم توجد صلاة اليوم، فالصلاة القادمة هي فجر الغد
        if prayers_today:
            first_prayer = prayers_today[0]
            time_diff = (24 * 60) - current_minutes + first_prayer[1]
            hours = time_diff // 60
            minutes = time_diff % 60
            
            time_str = f"{hours}:{minutes:02d}"
            return prayer_names[first_prayer[0]], time_str
        
        return None
    
    def update_islamic_text(self):
        """تحديث النص الإسلامي"""
        if not self.is_running:
            return
        
        current_text = self.islamic_texts[self.current_text_index]
        self.islamic_text_label.config(text=current_text)
        
        self.current_text_index = (self.current_text_index + 1) % len(self.islamic_texts)
        
        # جدولة التحديث التالي (كل 10 ثوانٍ)
        self.root.after(10000, self.update_islamic_text)
    
    def update_dates(self):
        """تحديث التواريخ"""
        if not self.is_running:
            return
        
        now = datetime.now()
        
        # التاريخ الميلادي
        gregorian_date = now.strftime("%A %d %B %Y")
        
        # ترجمة بسيطة
        day_names = {
            'Monday': 'الاثنين', 'Tuesday': 'الثلاثاء', 'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس', 'Friday': 'الجمعة', 'Saturday': 'السبت', 'Sunday': 'الأحد'
        }
        
        month_names = {
            'January': 'يناير', 'February': 'فبراير', 'March': 'مارس', 'April': 'أبريل',
            'May': 'مايو', 'June': 'يونيو', 'July': 'يوليو', 'August': 'أغسطس',
            'September': 'سبتمبر', 'October': 'أكتوبر', 'November': 'نوفمبر', 'December': 'ديسمبر'
        }
        
        for eng, ar in day_names.items():
            gregorian_date = gregorian_date.replace(eng, ar)
        for eng, ar in month_names.items():
            gregorian_date = gregorian_date.replace(eng, ar)
        
        self.gregorian_date_label.config(text=gregorian_date)
        
        # جدولة التحديث التالي (كل دقيقة)
        self.root.after(60000, self.update_dates)
    
    def show_admin_info(self, event=None):
        """عرض معلومات لوحة التحكم"""
        info_text = """لوحة التحكم الإدارية

هذه نسخة مبسطة من البرنامج.

في النسخة الكاملة، يمكنك:
• تعديل أوقات الصلاة
• تخصيص أصوات الأذان
• إدارة النصوص الإسلامية
• تعديل إعدادات العرض
• تحديد الموقع الجغرافي

اختصارات لوحة المفاتيح:
• A: عرض هذه الرسالة
• F11 أو Escape: تبديل ملء الشاشة
• Alt+F4: إغلاق البرنامج"""
        
        messagebox.showinfo("لوحة التحكم", info_text)
    
    def toggle_fullscreen(self, event=None):
        """تبديل وضع ملء الشاشة"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)
        
        if not current_state:
            self.root.state('zoomed')
    
    def quit_app(self):
        """إنهاء التطبيق"""
        self.is_running = False
        self.root.quit()
        self.root.destroy()

def main():
    """تشغيل التطبيق"""
    try:
        root = tk.Tk()
        app = SimpleMosqueApp(root)
        
        # ربط إغلاق النافذة
        root.protocol("WM_DELETE_WINDOW", app.quit_app)
        
        # تشغيل التطبيق
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
