#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة إعدادات التطبيق
"""

import json
import os
from pathlib import Path
import configparser

class ConfigManager:
    """مدير إعدادات التطبيق"""
    
    def __init__(self):
        self.config_dir = Path("config")
        self.config_file = self.config_dir / "settings.json"
        self.ini_file = self.config_dir / "app.ini"
        
        # إنشاء مجلد الإعدادات إذا لم يكن موجوداً
        self.config_dir.mkdir(exist_ok=True)
        
        # الإعدادات الافتراضية
        self.default_settings = {
            "mosque_name": "اسم المسجد",
            "location": {
                "latitude": 36.7538,
                "longitude": 3.0588,
                "city": "الجزائر",
                "country": "DZ"
            },
            "prayer_times": {
                "fajr": {"azan": "04:11", "iqama": "04:21"},
                "sunrise": {"azan": "05:37", "iqama": "05:52"},
                "dhuhr": {"azan": "12:20", "iqama": "12:30"},
                "asr": {"azan": "15:37", "iqama": "15:47"},
                "maghrib": {"azan": "19:01", "iqama": "19:11"},
                "isha": {"azan": "20:31", "iqama": "20:41"}
            },
            "audio": {
                "volume": 0.8,
                "azan_enabled": True,
                "test_mode": False
            },
            "display": {
                "fullscreen": True,
                "font_size": "medium",
                "theme": "red",
                "show_seconds": True,
                "animation_enabled": True
            },
            "islamic_texts": [
                "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
                "إِنَّ مَعَ الْعُسْرِ يُسْرًا",
                "وَاللَّهُ خَيْرٌ حَافِظًا وَهُوَ أَرْحَمُ الرَّاحِمِينَ",
                "قال رسول الله ﷺ: الصلاة نور",
                "قال رسول الله ﷺ: خير الناس أنفعهم للناس",
                "وَبَشِّرِ الصَّابِرِينَ",
                "رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً"
            ],
            "text_rotation": {
                "enabled": True,
                "interval": 10,  # ثواني
                "fade_effect": True
            },
            "weather": {
                "enabled": True,
                "api_key": "",
                "update_interval": 30,  # دقائق
                "show_details": True
            },
            "updates": {
                "prayer_times_interval": 60,  # دقائق
                "weather_interval": 30,  # دقائق
                "auto_update": True
            }
        }
        
        # تحميل الإعدادات
        self.settings = self.load_settings()
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # دمج الإعدادات المحملة مع الافتراضية
                settings = self.default_settings.copy()
                self.merge_settings(settings, loaded_settings)
                return settings
            else:
                # إنشاء ملف الإعدادات الافتراضي
                self.save_settings(self.default_settings)
                return self.default_settings.copy()
                
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            return self.default_settings.copy()
    
    def merge_settings(self, default, loaded):
        """دمج الإعدادات المحملة مع الافتراضية"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self.merge_settings(default[key], value)
                else:
                    default[key] = value
    
    def save_settings(self, settings=None):
        """حفظ الإعدادات في الملف"""
        try:
            if settings is None:
                settings = self.settings
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            print("تم حفظ الإعدادات بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get_setting(self, key_path, default=None):
        """الحصول على إعداد معين باستخدام مسار النقاط"""
        try:
            keys = key_path.split('.')
            value = self.settings
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set_setting(self, key_path, value):
        """تعديل إعداد معين باستخدام مسار النقاط"""
        try:
            keys = key_path.split('.')
            setting_dict = self.settings
            
            # الانتقال إلى المستوى الأخير
            for key in keys[:-1]:
                if key not in setting_dict:
                    setting_dict[key] = {}
                setting_dict = setting_dict[key]
            
            # تعديل القيمة
            setting_dict[keys[-1]] = value
            return True
            
        except Exception as e:
            print(f"خطأ في تعديل الإعداد {key_path}: {e}")
            return False
    
    def get_mosque_name(self):
        """الحصول على اسم المسجد"""
        return self.get_setting('mosque_name', 'اسم المسجد')
    
    def set_mosque_name(self, name):
        """تعديل اسم المسجد"""
        return self.set_setting('mosque_name', name)
    
    def get_location(self):
        """الحصول على الموقع الجغرافي"""
        return self.get_setting('location', {
            'latitude': 36.7538,
            'longitude': 3.0588,
            'city': 'الجزائر',
            'country': 'DZ'
        })
    
    def set_location(self, latitude, longitude, city=None, country=None):
        """تعديل الموقع الجغرافي"""
        location = {
            'latitude': latitude,
            'longitude': longitude
        }
        
        if city:
            location['city'] = city
        if country:
            location['country'] = country
        
        return self.set_setting('location', location)
    
    def get_prayer_times(self):
        """الحصول على أوقات الصلاة"""
        return self.get_setting('prayer_times', self.default_settings['prayer_times'])
    
    def set_prayer_times(self, prayer_times):
        """تعديل أوقات الصلاة"""
        return self.set_setting('prayer_times', prayer_times)
    
    def get_audio_settings(self):
        """الحصول على إعدادات الصوت"""
        return self.get_setting('audio', self.default_settings['audio'])
    
    def set_volume(self, volume):
        """تعديل مستوى الصوت"""
        return self.set_setting('audio.volume', max(0.0, min(1.0, volume)))
    
    def get_islamic_texts(self):
        """الحصول على النصوص الإسلامية"""
        return self.get_setting('islamic_texts', self.default_settings['islamic_texts'])
    
    def set_islamic_texts(self, texts):
        """تعديل النصوص الإسلامية"""
        return self.set_setting('islamic_texts', texts)
    
    def add_islamic_text(self, text):
        """إضافة نص إسلامي جديد"""
        texts = self.get_islamic_texts()
        if text not in texts:
            texts.append(text)
            return self.set_islamic_texts(texts)
        return False
    
    def remove_islamic_text(self, text):
        """حذف نص إسلامي"""
        texts = self.get_islamic_texts()
        if text in texts:
            texts.remove(text)
            return self.set_islamic_texts(texts)
        return False
    
    def get_display_settings(self):
        """الحصول على إعدادات العرض"""
        return self.get_setting('display', self.default_settings['display'])
    
    def is_fullscreen(self):
        """التحقق من وضع ملء الشاشة"""
        return self.get_setting('display.fullscreen', True)
    
    def set_fullscreen(self, fullscreen):
        """تعديل وضع ملء الشاشة"""
        return self.set_setting('display.fullscreen', fullscreen)
    
    def get_weather_settings(self):
        """الحصول على إعدادات الطقس"""
        return self.get_setting('weather', self.default_settings['weather'])
    
    def is_weather_enabled(self):
        """التحقق من تفعيل عرض الطقس"""
        return self.get_setting('weather.enabled', True)
    
    def get_update_intervals(self):
        """الحصول على فترات التحديث"""
        return self.get_setting('updates', self.default_settings['updates'])
    
    def export_settings(self, file_path):
        """تصدير الإعدادات إلى ملف"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {e}")
            return False
    
    def import_settings(self, file_path):
        """استيراد الإعدادات من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # دمج الإعدادات المستوردة
            self.merge_settings(self.settings, imported_settings)
            
            # حفظ الإعدادات الجديدة
            return self.save_settings()
            
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {e}")
            return False
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.settings = self.default_settings.copy()
        return self.save_settings()
    
    def create_backup(self):
        """إنشاء نسخة احتياطية من الإعدادات"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.config_dir / f"settings_backup_{timestamp}.json"
            
            return self.export_settings(backup_file)
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
