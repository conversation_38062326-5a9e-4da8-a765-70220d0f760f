مجلد الملفات الصوتية للأذان
Audio Files Directory

هذا المجلد مخصص لحفظ ملفات الأذان الصوتية.

الملفات المطلوبة:
================

1. azan_fajr.mp3
   - أذان الفجر
   - يُشغل عند وقت أذان الفجر

2. azan_normal.mp3
   - أذان باقي الصلوات
   - يُشغل عند أوقات: الظهر، العصر، المغرب، العشاء

التنسيقات المدعومة:
==================
- MP3 (مُفضل)
- WAV
- OGG

مواصفات الملفات:
================
- الجودة: عالية (320 kbps للـ MP3)
- المدة: 3-5 دقائق عادة
- الحجم: أقل من 10 ميجابايت لكل ملف
- اللغة: العربية

مصادر الحصول على ملفات الأذان:
==============================
1. تسجيلات من المساجد المحلية
2. مواقع الأذان الإسلامية المعتمدة
3. تطبيقات الأذان الموثوقة
4. مكتبات الصوتيات الإسلامية

ملاحظات مهمة:
=============
- تأكد من جودة الصوت قبل الاستخدام
- اختبر الملفات في البرنامج قبل الاستخدام الفعلي
- احتفظ بنسخ احتياطية من الملفات
- تأكد من الحقوق القانونية للملفات المستخدمة

في حالة عدم وجود ملفات صوتية:
=============================
- سيستخدم البرنامج نظام النطق الصوتي (Text-to-Speech)
- سيتم الإعلان عن وقت الصلاة بالصوت
- يمكن تعديل مستوى الصوت من لوحة التحكم

اختبار الملفات:
===============
1. ضع الملفات في هذا المجلد
2. شغل البرنامج
3. اضغط A لفتح لوحة التحكم
4. انتقل إلى تبويب "إعدادات الصوت"
5. اضغط "تجربة الأذان" للاختبار

استكشاف الأخطاء:
================
- إذا لم يعمل الصوت، تحقق من:
  * أسماء الملفات صحيحة
  * تنسيق الملفات مدعوم
  * مستوى الصوت في النظام
  * مستوى الصوت في البرنامج

للمساعدة:
==========
راجع ملف README_PYTHON.md للتفاصيل الكاملة

---
جزاكم الله خيراً على استخدام هذا البرنامج في خدمة دين الله 🕌
