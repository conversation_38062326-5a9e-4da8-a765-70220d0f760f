#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكون المحتوى الرئيسي - عرض الوقت وأوقات الصلاة والعد التنازلي
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
import math
import arabic_reshaper
from bidi.algorithm import get_display

class MainContentFrame(tk.Frame):
    """إطار المحتوى الرئيسي"""
    
    def __init__(self, parent, app):
        super().__init__(parent, bg='#8B0000')
        self.app = app
        self.prayer_times = {}
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المحتوى الرئيسي"""
        # تقسيم إلى ثلاثة أعمدة
        self.grid_columnconfigure(0, weight=1, minsize=200)
        self.grid_columnconfigure(1, weight=3)
        self.grid_columnconfigure(2, weight=1, minsize=200)
        self.grid_rowconfigure(0, weight=1)
        
        # العمود الأيسر - دائرة المسجد
        self.left_circle = self.create_circle_frame(0, "🕌", "المسجد")
        
        # العمود الأوسط - الوقت وأوقات الصلاة
        self.center_frame = tk.Frame(self, bg='#8B0000')
        self.center_frame.grid(row=0, column=1, sticky='nsew', padx=40)
        
        self.setup_center_content()
        
        # العمود الأيمن - العد التنازلي
        self.right_circle = self.create_circle_frame(2, "1:58", "الفجر")
    
    def create_circle_frame(self, column, main_text, sub_text):
        """إنشاء إطار دائري"""
        circle_frame = tk.Frame(self, bg='#8B0000')
        circle_frame.grid(row=0, column=column, sticky='nsew')
        
        # إنشاء Canvas للدائرة
        canvas = tk.Canvas(
            circle_frame,
            width=200,
            height=200,
            bg='#8B0000',
            highlightthickness=0
        )
        canvas.pack(expand=True)
        
        # رسم الدائرة
        circle = canvas.create_oval(
            10, 10, 190, 190,
            outline='white',
            width=3,
            fill='#DC143C'
        )
        
        # النص الرئيسي
        main_label = canvas.create_text(
            100, 80,
            text=main_text,
            font=self.app.get_font('large'),
            fill='white',
            anchor='center'
        )
        
        # النص الفرعي
        sub_label = canvas.create_text(
            100, 130,
            text=self.format_arabic_text(sub_text),
            font=self.app.get_font('medium'),
            fill='white',
            anchor='center'
        )
        
        return {
            'frame': circle_frame,
            'canvas': canvas,
            'main_label': main_label,
            'sub_label': sub_label
        }
    
    def setup_center_content(self):
        """إعداد المحتوى الأوسط"""
        # إطار الوقت
        self.time_frame = tk.Frame(self.center_frame, bg='#8B0000')
        self.time_frame.pack(pady=(0, 30))
        
        # الساعة الرقمية
        self.time_label = tk.Label(
            self.time_frame,
            text="2:13",
            font=self.app.get_font('time'),
            bg='#8B0000',
            fg='white'
        )
        self.time_label.pack()
        
        # فترة اليوم
        self.period_label = tk.Label(
            self.time_frame,
            text=self.format_arabic_text("صباحاً"),
            font=self.app.get_font('medium'),
            bg='#8B0000',
            fg='white'
        )
        self.period_label.pack()
        
        # إطار أوقات الصلاة
        self.prayer_frame = tk.Frame(self.center_frame, bg='#8B0000')
        self.prayer_frame.pack(fill=tk.X)
        
        self.setup_prayer_times_grid()
    
    def setup_prayer_times_grid(self):
        """إعداد شبكة أوقات الصلاة"""
        # أسماء الصلوات
        prayers = [
            ('الفجر', 'fajr'),
            ('الشروق', 'sunrise'),
            ('الظهر', 'dhuhr'),
            ('العصر', 'asr'),
            ('المغرب', 'maghrib'),
            ('العشاء', 'isha')
        ]
        
        # الأوقات الافتراضية
        default_times = {
            'fajr': {'azan': '4:11', 'iqama': '4:21'},
            'sunrise': {'azan': '5:37', 'iqama': '5:52'},
            'dhuhr': {'azan': '12:20', 'iqama': '12:30'},
            'asr': {'azan': '15:37', 'iqama': '15:47'},
            'maghrib': {'azan': '19:01', 'iqama': '19:11'},
            'isha': {'azan': '20:31', 'iqama': '20:41'}
        }
        
        self.prayer_cards = {}
        
        # إنشاء بطاقات الصلوات
        for i, (prayer_name, prayer_key) in enumerate(prayers):
            # إطار البطاقة
            card_frame = tk.Frame(
                self.prayer_frame,
                bg='#B22222',
                relief=tk.RAISED,
                bd=2
            )
            card_frame.grid(row=0, column=i, padx=10, pady=10, sticky='ew')
            
            # اسم الصلاة
            name_label = tk.Label(
                card_frame,
                text=self.format_arabic_text(prayer_name),
                font=self.app.get_font('medium'),
                bg='#B22222',
                fg='white'
            )
            name_label.pack(pady=(10, 5))
            
            # وقت الأذان
            azan_time = default_times[prayer_key]['azan']
            azan_label = tk.Label(
                card_frame,
                text=azan_time,
                font=self.app.get_font('title'),
                bg='#B22222',
                fg='white'
            )
            azan_label.pack(pady=2)
            
            # وقت الإقامة
            iqama_time = default_times[prayer_key]['iqama']
            iqama_label = tk.Label(
                card_frame,
                text=iqama_time,
                font=self.app.get_font('medium'),
                bg='#B22222',
                fg='white'
            )
            iqama_label.pack(pady=(2, 10))
            
            # حفظ المراجع
            self.prayer_cards[prayer_key] = {
                'frame': card_frame,
                'name': name_label,
                'azan': azan_label,
                'iqama': iqama_label
            }
        
        # توزيع الأعمدة بالتساوي
        for i in range(6):
            self.prayer_frame.grid_columnconfigure(i, weight=1)
    
    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
    
    def update_time(self, current_time):
        """تحديث عرض الوقت"""
        # تنسيق الوقت
        time_str = current_time.strftime("%H:%M")
        self.time_label.config(text=time_str)
        
        # تحديد فترة اليوم
        hour = current_time.hour
        if 5 <= hour < 12:
            period = "صباحاً"
        elif 12 <= hour < 17:
            period = "ظهراً"
        elif 17 <= hour < 20:
            period = "عصراً"
        elif 20 <= hour < 24:
            period = "مساءً"
        else:
            period = "ليلاً"
        
        self.period_label.config(text=self.format_arabic_text(period))
        
        # تحديث العد التنازلي
        self.update_countdown(current_time)
    
    def update_prayer_times(self, prayer_times):
        """تحديث أوقات الصلاة"""
        self.prayer_times = prayer_times
        
        for prayer_key, times in prayer_times.items():
            if prayer_key in self.prayer_cards:
                card = self.prayer_cards[prayer_key]
                
                # تحديث وقت الأذان
                azan_time = times.get('azan', '00:00')
                card['azan'].config(text=azan_time)
                
                # تحديث وقت الإقامة
                iqama_time = times.get('iqama', '00:00')
                card['iqama'].config(text=iqama_time)
    
    def update_countdown(self, current_time):
        """تحديث العد التنازلي للصلاة القادمة"""
        if not self.prayer_times:
            return
        
        # البحث عن الصلاة القادمة
        next_prayer = self.find_next_prayer(current_time)
        
        if next_prayer:
            prayer_name, time_remaining = next_prayer
            
            # تحديث النص في الدائرة اليمنى
            self.right_circle['canvas'].itemconfig(
                self.right_circle['main_label'],
                text=time_remaining
            )
            self.right_circle['canvas'].itemconfig(
                self.right_circle['sub_label'],
                text=self.format_arabic_text(prayer_name)
            )
    
    def find_next_prayer(self, current_time):
        """البحث عن الصلاة القادمة"""
        prayer_names = {
            'fajr': 'الفجر',
            'dhuhr': 'الظهر',
            'asr': 'العصر',
            'maghrib': 'المغرب',
            'isha': 'العشاء'
        }
        
        current_minutes = current_time.hour * 60 + current_time.minute
        
        # قائمة الصلوات مع أوقاتها
        prayers_today = []
        for prayer_key, times in self.prayer_times.items():
            if prayer_key == 'sunrise':  # تجاهل الشروق
                continue
                
            azan_time = times.get('azan', '00:00')
            try:
                hour, minute = map(int, azan_time.split(':'))
                prayer_minutes = hour * 60 + minute
                prayers_today.append((prayer_key, prayer_minutes))
            except:
                continue
        
        # ترتيب الصلوات حسب الوقت
        prayers_today.sort(key=lambda x: x[1])
        
        # البحث عن الصلاة القادمة
        for prayer_key, prayer_minutes in prayers_today:
            if prayer_minutes > current_minutes:
                time_diff = prayer_minutes - current_minutes
                hours = time_diff // 60
                minutes = time_diff % 60
                
                if hours > 0:
                    time_str = f"{hours}:{minutes:02d}"
                else:
                    time_str = f"{minutes:02d}"
                
                return prayer_names[prayer_key], time_str
        
        # إذا لم توجد صلاة اليوم، فالصلاة القادمة هي فجر الغد
        if prayers_today:
            first_prayer = prayers_today[0]
            time_diff = (24 * 60) - current_minutes + first_prayer[1]
            hours = time_diff // 60
            minutes = time_diff % 60
            
            time_str = f"{hours}:{minutes:02d}"
            return prayer_names[first_prayer[0]], time_str
        
        return None
