@echo off
chcp 65001 >nul
echo.
echo ========================================
echo      شاشة أوقات الصلاة للمسجد
echo    Mosque Prayer Times Display
echo ========================================
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت
    echo.
    echo يرجى تشغيل install_requirements.bat أولاً
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo اختر نوع التشغيل:
echo.
echo 1. النسخة المبسطة (موصى بها للبداية)
echo 2. النسخة الكاملة (جميع الميزات)
echo 3. بناء ملف تنفيذي
echo 4. تثبيت/تحديث المتطلبات
echo 5. خروج
echo.

set /p choice="اختيارك (1-5): "

if "%choice%"=="1" goto :simple
if "%choice%"=="2" goto :full
if "%choice%"=="3" goto :build
if "%choice%"=="4" goto :install
if "%choice%"=="5" goto :exit
goto :invalid

:simple
echo.
echo 🚀 تشغيل النسخة المبسطة...
echo.
echo 💡 نصائح:
echo   - اضغط A لفتح لوحة التحكم
echo   - اضغط F11 لملء الشاشة
echo   - اضغط Escape للخروج من ملء الشاشة
echo.
python run_simple.py
goto :end

:full
echo.
echo 🚀 تشغيل النسخة الكاملة...
echo.
echo 💡 نصائح:
echo   - اضغط A لفتح لوحة التحكم الإدارية
echo   - ضع ملفات الأذان في مجلد audio
echo   - تأكد من اتصال الإنترنت لجلب أوقات الصلاة
echo.
python main.py
goto :end

:build
echo.
echo 🔨 بناء ملف تنفيذي...
echo.
python build_exe.py
goto :end

:install
echo.
echo 📦 تثبيت/تحديث المتطلبات...
echo.
call install_requirements.bat
goto :end

:invalid
echo.
echo ❌ اختيار غير صحيح
echo.
pause
goto :start

:exit
echo.
echo 👋 شكراً لاستخدام البرنامج
goto :end

:end
echo.
echo 🕌 جزاكم الله خيراً
echo.
pause
