#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التحكم عن بُعد
Remote Control System
"""

import tkinter as tk
from tkinter import ttk
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
import socket
import hashlib
import secrets
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import ssl

class CommandType(Enum):
    """أنواع الأوامر"""
    UPDATE_PRAYER_TIMES = "update_prayer_times"
    CHANGE_VOLUME = "change_volume"
    PLAY_AZAN = "play_azan"
    STOP_AZAN = "stop_azan"
    UPDATE_CONTENT = "update_content"
    CHANGE_SETTINGS = "change_settings"
    RESTART_SYSTEM = "restart_system"
    SHUTDOWN_SYSTEM = "shutdown_system"
    GET_STATUS = "get_status"
    UPDATE_ISLAMIC_TEXTS = "update_islamic_texts"
    SCHEDULE_ANNOUNCEMENT = "schedule_announcement"

class UserRole(Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"
    MODERATOR = "moderator"
    VIEWER = "viewer"

class RemoteCommand:
    """أمر التحكم عن بُعد"""
    
    def __init__(self,
                 command_id: str,
                 command_type: CommandType,
                 parameters: Dict,
                 user_id: str,
                 timestamp: datetime = None):
        
        self.id = command_id
        self.type = command_type
        self.parameters = parameters
        self.user_id = user_id
        self.timestamp = timestamp or datetime.now()
        self.executed = False
        self.result = None
        self.error = None

class RemoteUser:
    """مستخدم التحكم عن بُعد"""
    
    def __init__(self,
                 user_id: str,
                 username: str,
                 password_hash: str,
                 role: UserRole,
                 permissions: List[str] = None):
        
        self.id = user_id
        self.username = username
        self.password_hash = password_hash
        self.role = role
        self.permissions = permissions or []
        self.last_login = None
        self.is_active = True
        self.session_token = None

class RemoteControlServer(BaseHTTPRequestHandler):
    """خادم التحكم عن بُعد"""
    
    def __init__(self, *args, remote_system=None, **kwargs):
        self.remote_system = remote_system
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """معالجة طلبات GET"""
        try:
            if self.path == '/':
                self.serve_dashboard()
            elif self.path == '/api/status':
                self.serve_status()
            elif self.path.startswith('/api/'):
                self.handle_api_request()
            else:
                self.serve_static_file()
                
        except Exception as e:
            self.send_error_response(500, str(e))
    
    def do_POST(self):
        """معالجة طلبات POST"""
        try:
            if self.path.startswith('/api/'):
                self.handle_api_request()
            else:
                self.send_error_response(404, "Not Found")
                
        except Exception as e:
            self.send_error_response(500, str(e))
    
    def serve_dashboard(self):
        """تقديم لوحة التحكم الرئيسية"""
        html_content = self.get_dashboard_html()
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', len(html_content.encode('utf-8')))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_status(self):
        """تقديم حالة النظام"""
        if self.remote_system:
            status = self.remote_system.get_system_status()
        else:
            status = {"error": "Remote system not available"}
        
        self.send_json_response(status)
    
    def handle_api_request(self):
        """معالجة طلبات API"""
        # استخراج المسار والمعاملات
        path_parts = self.path.split('/')
        
        if len(path_parts) < 3:
            self.send_error_response(400, "Invalid API path")
            return
        
        endpoint = path_parts[2]
        
        # التحقق من المصادقة
        if not self.authenticate_request():
            self.send_error_response(401, "Unauthorized")
            return
        
        # معالجة النقاط المختلفة
        if endpoint == 'command':
            self.handle_command_request()
        elif endpoint == 'settings':
            self.handle_settings_request()
        elif endpoint == 'status':
            self.serve_status()
        else:
            self.send_error_response(404, "API endpoint not found")
    
    def authenticate_request(self) -> bool:
        """التحقق من صحة الطلب"""
        # التحقق من رمز المصادقة في الرأس
        auth_header = self.headers.get('Authorization')
        if not auth_header:
            return False
        
        try:
            token = auth_header.split(' ')[1]
            return self.remote_system.validate_session_token(token) if self.remote_system else False
        except:
            return False
    
    def handle_command_request(self):
        """معالجة طلب الأمر"""
        if self.command == 'POST':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                command_data = json.loads(post_data.decode('utf-8'))
                result = self.remote_system.execute_remote_command(command_data)
                self.send_json_response(result)
            except json.JSONDecodeError:
                self.send_error_response(400, "Invalid JSON")
        else:
            self.send_error_response(405, "Method not allowed")
    
    def send_json_response(self, data: Dict):
        """إرسال استجابة JSON"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', len(json_data.encode('utf-8')))
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))
    
    def send_error_response(self, code: int, message: str):
        """إرسال استجابة خطأ"""
        error_data = {"error": message, "code": code}
        json_data = json.dumps(error_data, ensure_ascii=False)
        
        self.send_response(code)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', len(json_data.encode('utf-8')))
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))
    
    def get_dashboard_html(self) -> str:
        """الحصول على HTML لوحة التحكم"""
        return '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم عن بُعد - شاشة أوقات الصلاة</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; min-height: 100vh; direction: rtl;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .card { 
            background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);
            border-radius: 15px; padding: 20px; margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .btn { 
            background: #4CAF50; color: white; border: none; padding: 12px 24px;
            border-radius: 8px; cursor: pointer; font-size: 16px; margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover { background: #45a049; }
        .btn.danger { background: #f44336; }
        .btn.danger:hover { background: #da190b; }
        .status { display: flex; align-items: center; gap: 10px; margin: 10px 0; }
        .status-dot { width: 12px; height: 12px; border-radius: 50%; }
        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        input, select, textarea { 
            width: 100%; padding: 10px; margin: 5px 0; border: none;
            border-radius: 5px; background: rgba(255,255,255,0.2);
            color: white; font-size: 16px;
        }
        input::placeholder { color: rgba(255,255,255,0.7); }
        .prayer-times { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; }
        .prayer-card { 
            background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕌 لوحة التحكم عن بُعد</h1>
            <p>إدارة شاشة أوقات الصلاة من أي مكان</p>
            <div class="status">
                <span class="status-dot status-online"></span>
                <span>متصل</span>
                <span id="last-update">آخر تحديث: الآن</span>
            </div>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>🎵 التحكم في الصوت</h3>
                <label>مستوى الصوت:</label>
                <input type="range" id="volume" min="0" max="100" value="80">
                <span id="volume-value">80%</span>
                <div>
                    <button class="btn" onclick="playAzan()">تشغيل الأذان</button>
                    <button class="btn danger" onclick="stopAzan()">إيقاف الصوت</button>
                </div>
            </div>
            
            <div class="card">
                <h3>⏰ أوقات الصلاة</h3>
                <div class="prayer-times">
                    <div class="prayer-card">
                        <h4>الفجر</h4>
                        <input type="time" id="fajr" value="04:11">
                    </div>
                    <div class="prayer-card">
                        <h4>الظهر</h4>
                        <input type="time" id="dhuhr" value="12:20">
                    </div>
                    <div class="prayer-card">
                        <h4>العصر</h4>
                        <input type="time" id="asr" value="15:37">
                    </div>
                    <div class="prayer-card">
                        <h4>المغرب</h4>
                        <input type="time" id="maghrib" value="19:01">
                    </div>
                    <div class="prayer-card">
                        <h4>العشاء</h4>
                        <input type="time" id="isha" value="20:31">
                    </div>
                </div>
                <button class="btn" onclick="updatePrayerTimes()">تحديث الأوقات</button>
            </div>
            
            <div class="card">
                <h3>📢 الإعلانات</h3>
                <textarea id="announcement" placeholder="اكتب الإعلان هنا..." rows="4"></textarea>
                <label>مدة العرض (ثواني):</label>
                <input type="number" id="duration" value="30" min="5" max="300">
                <button class="btn" onclick="sendAnnouncement()">إرسال الإعلان</button>
            </div>
            
            <div class="card">
                <h3>📊 حالة النظام</h3>
                <div id="system-status">
                    <p>🔄 جاري التحميل...</p>
                </div>
                <button class="btn" onclick="refreshStatus()">تحديث الحالة</button>
            </div>
        </div>
    </div>
    
    <script>
        // تحديث مستوى الصوت
        document.getElementById('volume').addEventListener('input', function() {
            document.getElementById('volume-value').textContent = this.value + '%';
        });
        
        // وظائف التحكم
        function playAzan() {
            sendCommand('play_azan', {});
        }
        
        function stopAzan() {
            sendCommand('stop_azan', {});
        }
        
        function updatePrayerTimes() {
            const times = {
                fajr: document.getElementById('fajr').value,
                dhuhr: document.getElementById('dhuhr').value,
                asr: document.getElementById('asr').value,
                maghrib: document.getElementById('maghrib').value,
                isha: document.getElementById('isha').value
            };
            sendCommand('update_prayer_times', times);
        }
        
        function sendAnnouncement() {
            const announcement = document.getElementById('announcement').value;
            const duration = document.getElementById('duration').value;
            
            if (!announcement.trim()) {
                alert('يرجى كتابة الإعلان');
                return;
            }
            
            sendCommand('schedule_announcement', {
                message: announcement,
                duration: parseInt(duration)
            });
        }
        
        function sendCommand(type, parameters) {
            fetch('/api/command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer demo-token'
                },
                body: JSON.stringify({
                    type: type,
                    parameters: parameters
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم تنفيذ الأمر بنجاح');
                } else {
                    alert('خطأ: ' + data.error);
                }
            })
            .catch(error => {
                alert('خطأ في الاتصال: ' + error);
            });
        }
        
        function refreshStatus() {
            fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('system-status');
                statusDiv.innerHTML = `
                    <p>⚡ وقت التشغيل: ${data.uptime || 'غير متوفر'}</p>
                    <p>💾 استخدام الذاكرة: ${data.memory_usage || 0}%</p>
                    <p>🖥️ استخدام المعالج: ${data.cpu_usage || 0}%</p>
                    <p>📊 حالة النظام: ${data.status || 'غير معروف'}</p>
                `;
                document.getElementById('last-update').textContent = 'آخر تحديث: ' + new Date().toLocaleTimeString('ar');
            })
            .catch(error => {
                document.getElementById('system-status').innerHTML = '<p>❌ خطأ في جلب البيانات</p>';
            });
        }
        
        // تحديث الحالة كل 30 ثانية
        setInterval(refreshStatus, 30000);
        
        // تحديث أولي
        refreshStatus();
    </script>
</body>
</html>
        '''

class RemoteControlSystem:
    """نظام التحكم عن بُعد"""
    
    def __init__(self, parent_app):
        self.parent_app = parent_app
        self.users = {}
        self.active_sessions = {}
        self.command_queue = []
        self.command_handlers = {}
        
        # إعدادات الخادم
        self.settings = self.load_settings()
        self.server = None
        self.server_thread = None
        self.is_running = False
        
        # أمان
        self.secret_key = secrets.token_hex(32)
        
        self.initialize_remote_system()
        self.setup_command_handlers()
        self.load_users()
    
    def load_settings(self) -> Dict:
        """تحميل إعدادات التحكم عن بُعد"""
        default_settings = {
            "enabled": True,
            "port": 8080,
            "host": "0.0.0.0",
            "ssl_enabled": False,
            "ssl_cert_path": "",
            "ssl_key_path": "",
            "max_connections": 10,
            "session_timeout": 3600,  # ثانية
            "require_authentication": True,
            "allowed_ips": [],  # فارغ = جميع العناوين مسموحة
            "rate_limiting": {
                "enabled": True,
                "max_requests_per_minute": 60
            }
        }
        
        try:
            with open('config/remote_settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
        except:
            return default_settings
    
    def save_settings(self):
        """حفظ إعدادات التحكم عن بُعد"""
        try:
            with open('config/remote_settings.json', 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات التحكم عن بُعد: {e}")
    
    def initialize_remote_system(self):
        """تهيئة نظام التحكم عن بُعد"""
        try:
            # إنشاء مجلد البيانات
            import os
            os.makedirs('data/remote', exist_ok=True)
            
            print("✅ تم تهيئة نظام التحكم عن بُعد")
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة نظام التحكم عن بُعد: {e}")
    
    def setup_command_handlers(self):
        """إعداد معالجات الأوامر"""
        self.command_handlers = {
            CommandType.UPDATE_PRAYER_TIMES: self.handle_update_prayer_times,
            CommandType.CHANGE_VOLUME: self.handle_change_volume,
            CommandType.PLAY_AZAN: self.handle_play_azan,
            CommandType.STOP_AZAN: self.handle_stop_azan,
            CommandType.UPDATE_CONTENT: self.handle_update_content,
            CommandType.CHANGE_SETTINGS: self.handle_change_settings,
            CommandType.GET_STATUS: self.handle_get_status,
            CommandType.UPDATE_ISLAMIC_TEXTS: self.handle_update_islamic_texts,
            CommandType.SCHEDULE_ANNOUNCEMENT: self.handle_schedule_announcement
        }
    
    def load_users(self):
        """تحميل المستخدمين"""
        try:
            with open('data/remote/users.json', 'r', encoding='utf-8') as f:
                users_data = json.load(f)
                
                for user_data in users_data:
                    user = RemoteUser(
                        user_data["id"],
                        user_data["username"],
                        user_data["password_hash"],
                        UserRole(user_data["role"]),
                        user_data.get("permissions", [])
                    )
                    self.users[user.id] = user
                    
        except FileNotFoundError:
            # إنشاء مستخدم افتراضي
            self.create_default_admin()
        except Exception as e:
            print(f"خطأ في تحميل المستخدمين: {e}")
            self.create_default_admin()
    
    def create_default_admin(self):
        """إنشاء مستخدم إداري افتراضي"""
        try:
            admin_password = "admin123"
            password_hash = hashlib.sha256(admin_password.encode()).hexdigest()
            
            admin_user = RemoteUser(
                "admin",
                "admin",
                password_hash,
                UserRole.ADMIN,
                ["all"]
            )
            
            self.users["admin"] = admin_user
            self.save_users()
            
            print("✅ تم إنشاء مستخدم إداري افتراضي")
            print("   اسم المستخدم: admin")
            print("   كلمة المرور: admin123")
            print("   ⚠️ يرجى تغيير كلمة المرور فوراً!")
            
        except Exception as e:
            print(f"خطأ في إنشاء المستخدم الافتراضي: {e}")
    
    def save_users(self):
        """حفظ المستخدمين"""
        try:
            users_data = []
            for user in self.users.values():
                users_data.append({
                    "id": user.id,
                    "username": user.username,
                    "password_hash": user.password_hash,
                    "role": user.role.value,
                    "permissions": user.permissions
                })
            
            with open('data/remote/users.json', 'w', encoding='utf-8') as f:
                json.dump(users_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ المستخدمين: {e}")
    
    def start_server(self):
        """بدء خادم التحكم عن بُعد"""
        try:
            if not self.settings.get("enabled", True):
                print("⚠️ خادم التحكم عن بُعد معطل")
                return False
            
            host = self.settings.get("host", "0.0.0.0")
            port = self.settings.get("port", 8080)
            
            # إنشاء معالج مخصص
            def handler(*args, **kwargs):
                RemoteControlServer(*args, remote_system=self, **kwargs)
            
            # إنشاء الخادم
            self.server = HTTPServer((host, port), handler)
            
            # إعداد SSL إذا كان مفعلاً
            if self.settings.get("ssl_enabled", False):
                self.setup_ssl()
            
            # بدء الخادم في خيط منفصل
            self.server_thread = threading.Thread(
                target=self.run_server,
                daemon=True
            )
            self.server_thread.start()
            
            self.is_running = True
            
            print(f"🌐 تم بدء خادم التحكم عن بُعد على http://{host}:{port}")
            return True
            
        except Exception as e:
            print(f"خطأ في بدء خادم التحكم عن بُعد: {e}")
            return False
    
    def setup_ssl(self):
        """إعداد SSL"""
        try:
            cert_path = self.settings.get("ssl_cert_path")
            key_path = self.settings.get("ssl_key_path")
            
            if cert_path and key_path:
                context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                context.load_cert_chain(cert_path, key_path)
                self.server.socket = context.wrap_socket(self.server.socket, server_side=True)
                print("✅ تم تفعيل SSL")
            else:
                print("⚠️ مسارات شهادة SSL غير محددة")
                
        except Exception as e:
            print(f"خطأ في إعداد SSL: {e}")
    
    def run_server(self):
        """تشغيل الخادم"""
        try:
            self.server.serve_forever()
        except Exception as e:
            print(f"خطأ في تشغيل الخادم: {e}")
    
    def stop_server(self):
        """إيقاف الخادم"""
        try:
            if self.server:
                self.server.shutdown()
                self.server.server_close()
                self.is_running = False
                print("🛑 تم إيقاف خادم التحكم عن بُعد")
                
        except Exception as e:
            print(f"خطأ في إيقاف الخادم: {e}")
    
    def authenticate_user(self, username: str, password: str) -> Optional[str]:
        """مصادقة المستخدم"""
        try:
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            for user in self.users.values():
                if user.username == username and user.password_hash == password_hash:
                    if user.is_active:
                        # إنشاء رمز جلسة
                        session_token = secrets.token_hex(32)
                        user.session_token = session_token
                        user.last_login = datetime.now()
                        
                        self.active_sessions[session_token] = {
                            "user_id": user.id,
                            "created_at": datetime.now(),
                            "last_activity": datetime.now()
                        }
                        
                        return session_token
            
            return None
            
        except Exception as e:
            print(f"خطأ في مصادقة المستخدم: {e}")
            return None
    
    def validate_session_token(self, token: str) -> bool:
        """التحقق من صحة رمز الجلسة"""
        try:
            if token not in self.active_sessions:
                return False
            
            session = self.active_sessions[token]
            
            # التحقق من انتهاء الجلسة
            timeout = self.settings.get("session_timeout", 3600)
            if (datetime.now() - session["last_activity"]).total_seconds() > timeout:
                del self.active_sessions[token]
                return False
            
            # تحديث آخر نشاط
            session["last_activity"] = datetime.now()
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من رمز الجلسة: {e}")
            return False
    
    def execute_remote_command(self, command_data: Dict) -> Dict:
        """تنفيذ أمر عن بُعد"""
        try:
            command_type = CommandType(command_data["type"])
            parameters = command_data.get("parameters", {})
            
            # التحقق من الصلاحيات
            # user_id = self.get_user_from_session(session_token)
            # if not self.check_permission(user_id, command_type):
            #     return {"success": False, "error": "ليس لديك صلاحية لتنفيذ هذا الأمر"}
            
            # تنفيذ الأمر
            if command_type in self.command_handlers:
                result = self.command_handlers[command_type](parameters)
                return {"success": True, "result": result}
            else:
                return {"success": False, "error": "أمر غير مدعوم"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def handle_update_prayer_times(self, parameters: Dict) -> Dict:
        """معالجة تحديث أوقات الصلاة"""
        try:
            if hasattr(self.parent_app, 'prayer_times'):
                for prayer, time_str in parameters.items():
                    if prayer in self.parent_app.prayer_times:
                        self.parent_app.prayer_times[prayer]['azan'] = time_str
                
                # تحديث العرض
                if hasattr(self.parent_app, 'main_content_frame'):
                    self.parent_app.main_content_frame.update_prayer_times(self.parent_app.prayer_times)
                
                return {"message": "تم تحديث أوقات الصلاة"}
            
            return {"error": "فشل في تحديث أوقات الصلاة"}
            
        except Exception as e:
            return {"error": str(e)}
    
    def handle_change_volume(self, parameters: Dict) -> Dict:
        """معالجة تغيير مستوى الصوت"""
        try:
            volume = parameters.get("volume", 0.8)
            
            if hasattr(self.parent_app, 'azan_service'):
                self.parent_app.azan_service.set_volume(volume)
                return {"message": f"تم تعديل مستوى الصوت إلى {volume * 100}%"}
            
            return {"error": "خدمة الصوت غير متوفرة"}
            
        except Exception as e:
            return {"error": str(e)}
    
    def handle_play_azan(self, parameters: Dict) -> Dict:
        """معالجة تشغيل الأذان"""
        try:
            prayer_name = parameters.get("prayer", "dhuhr")
            
            if hasattr(self.parent_app, 'azan_service'):
                success = self.parent_app.azan_service.play_azan(prayer_name)
                if success:
                    return {"message": f"تم تشغيل أذان {prayer_name}"}
                else:
                    return {"error": "فشل في تشغيل الأذان"}
            
            return {"error": "خدمة الأذان غير متوفرة"}
            
        except Exception as e:
            return {"error": str(e)}
    
    def handle_stop_azan(self, parameters: Dict) -> Dict:
        """معالجة إيقاف الأذان"""
        try:
            if hasattr(self.parent_app, 'azan_service'):
                self.parent_app.azan_service.stop_azan()
                return {"message": "تم إيقاف الأذان"}
            
            return {"error": "خدمة الأذان غير متوفرة"}
            
        except Exception as e:
            return {"error": str(e)}
    
    def handle_schedule_announcement(self, parameters: Dict) -> Dict:
        """معالجة جدولة الإعلان"""
        try:
            message = parameters.get("message", "")
            duration = parameters.get("duration", 30)
            
            # يمكن ربطها مع نظام إدارة المحتوى
            print(f"📢 إعلان مجدول: {message} (مدة: {duration} ثانية)")
            
            return {"message": "تم جدولة الإعلان"}
            
        except Exception as e:
            return {"error": str(e)}
    
    def handle_get_status(self, parameters: Dict) -> Dict:
        """معالجة الحصول على حالة النظام"""
        try:
            status = {
                "uptime": "24 ساعة",
                "memory_usage": 45,
                "cpu_usage": 12,
                "status": "نشط",
                "last_update": datetime.now().isoformat(),
                "active_sessions": len(self.active_sessions)
            }
            
            return status
            
        except Exception as e:
            return {"error": str(e)}
    
    def handle_update_content(self, parameters: Dict) -> Dict:
        """معالجة تحديث المحتوى"""
        return {"message": "تم تحديث المحتوى"}
    
    def handle_change_settings(self, parameters: Dict) -> Dict:
        """معالجة تغيير الإعدادات"""
        return {"message": "تم تحديث الإعدادات"}
    
    def handle_update_islamic_texts(self, parameters: Dict) -> Dict:
        """معالجة تحديث النصوص الإسلامية"""
        return {"message": "تم تحديث النصوص الإسلامية"}
    
    def get_system_status(self) -> Dict:
        """الحصول على حالة النظام"""
        return {
            "server_running": self.is_running,
            "active_sessions": len(self.active_sessions),
            "total_users": len(self.users),
            "uptime": "متصل",
            "memory_usage": 45,
            "cpu_usage": 12,
            "status": "نشط"
        }
    
    def stop(self):
        """إيقاف نظام التحكم عن بُعد"""
        self.stop_server()
        print("🛑 تم إيقاف نظام التحكم عن بُعد")
