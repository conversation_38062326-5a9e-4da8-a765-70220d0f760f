# شاشة أوقات الصلاة للمسجد - نسخة Python
## Mosque Prayer Times Display - Python Version

برنامج مخصص لعرض توقيت الصلاة على شاشة المسجد مطور بلغة Python مع واجهة Tkinter ودعم كامل للغة العربية.

## 🌟 المميزات الرئيسية

### ✨ الواجهة الرسومية
- **تصميم أنيق**: خلفية حمراء مزخرفة تحاكي التصميم التقليدي للمساجد
- **دعم اللغة العربية**: عرض صحيح للنصوص العربية مع دعم الاتجاه RTL
- **ساعة رقمية كبيرة**: عرض الوقت الحالي بخط كبير وواضح
- **تصميم متجاوب**: يتكيف مع أحجام الشاشات المختلفة

### 🕐 أوقات الصلاة
- **حساب دقيق**: استخدام AlAdhan API لحساب الأوقات حسب الموقع الجغرافي
- **ستة أوقات**: الفجر، الشروق، الظهر، العصر، المغرب، العشاء
- **أوقات الإقامة**: عرض وقت الأذان ووقت الإقامة لكل صلاة
- **عد تنازلي**: دائرة تعرض الوقت المتبقي للصلاة القادمة

### 🔊 نظام الأذان
- **تشغيل تلقائي**: الأذان يُشغل تلقائياً في الأوقات المحددة
- **ملفات صوتية**: دعم ملفات MP3, WAV, OGG
- **نطق صوتي**: استخدام Text-to-Speech كبديل
- **تحكم في الصوت**: إمكانية تعديل مستوى الصوت

### 📖 النصوص الإسلامية
- **عرض متناوب**: آيات قرآنية وأحاديث نبوية تتغير كل 10 ثوانٍ
- **تنسيق جميل**: خط عربي أنيق مع تأثيرات بصرية
- **إدارة المحتوى**: إمكانية إضافة وحذف النصوص

### 🌤️ معلومات الطقس
- **بيانات حية**: جلب معلومات الطقس من APIs مجانية
- **علم الدولة**: يظهر تلقائياً حسب الموقع الجغرافي
- **درجة الحرارة**: عرض درجة الحرارة الحالية والمحسوسة

### ⚙️ لوحة التحكم الإدارية
- **واجهة سهلة**: نافذة إعدادات باللغة العربية
- **تعديل الأوقات**: تخصيص أوقات الأذان والإقامة
- **إعدادات الصوت**: تحكم في مستوى الصوت واختبار الأذان
- **إدارة النصوص**: إضافة وحذف الآيات والأحاديث
- **الموقع الجغرافي**: تحديد الإحداثيات للحساب الدقيق

## 📋 متطلبات النظام

### متطلبات أساسية
- **Python 3.8+** (يُفضل 3.9 أو أحدث)
- **نظام التشغيل**: Windows 10/11, Linux, macOS
- **الذاكرة**: 4 GB RAM كحد أدنى
- **مساحة القرص**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1920x1080 أو أعلى (مُحسَّن للشاشات الكبيرة)

### المكتبات المطلوبة
```
tkinter (مدمجة مع Python)
requests>=2.31.0
pygame>=2.5.0
Pillow>=10.0.0
python-bidi>=0.4.2
arabic-reshaper>=3.0.0
hijri-converter>=2.3.1
geopy>=2.3.0
pyttsx3>=2.90
schedule>=1.2.0
configparser>=6.0.0
pyinstaller>=5.13.0 (للبناء فقط)
```

## 🚀 التثبيت والتشغيل

### الطريقة الأولى: التشغيل المباشر

1. **تحميل المشروع**
```bash
git clone [repository-url]
cd mosque-prayer-display-python
```

2. **تثبيت المتطلبات**
```bash
# على Windows
install_requirements.bat

# أو يدوياً
pip install -r requirements.txt
```

3. **تشغيل النسخة المبسطة**
```bash
python run_simple.py
```

4. **تشغيل النسخة الكاملة**
```bash
python main.py
```

### الطريقة الثانية: بناء ملف تنفيذي

1. **بناء التطبيق**
```bash
python build_exe.py
```

2. **اختيار نوع البناء**
- النسخة المبسطة (أصغر حجماً، ميزات أساسية)
- النسخة الكاملة (جميع الميزات)
- كلاهما

3. **تشغيل الملف التنفيذي**
```bash
# من مجلد dist
./MosquePrayerDisplay_Simple.exe
# أو
./MosquePrayerDisplay.exe
```

## 🎮 كيفية الاستخدام

### التشغيل العادي
- البرنامج يعمل تلقائياً عند التشغيل
- الوقت والتاريخ يتحدثان مباشرة
- النصوص الإسلامية تتغير كل 10 ثوانٍ
- أوقات الصلاة تُحدث تلقائياً حسب الموقع

### اختصارات لوحة المفاتيح
- **A**: فتح لوحة التحكم الإدارية
- **Escape**: إغلاق لوحة التحكم أو تبديل ملء الشاشة
- **F11**: تبديل وضع ملء الشاشة
- **Alt+F4**: إغلاق البرنامج

### لوحة التحكم الإدارية

#### تبويب أوقات الصلاة
- تعديل وقت الأذان لكل صلاة
- تعديل وقت الإقامة لكل صلاة
- اختبار الأذان لكل صلاة
- حفظ التغييرات تلقائياً

#### تبويب إعدادات الصوت
- شريط تمرير لتعديل مستوى الصوت (0-100%)
- زر اختبار الأذان العام
- زر إيقاف الصوت الحالي

#### تبويب النصوص الإسلامية
- إضافة نصوص جديدة (آيات أو أحاديث)
- عرض قائمة النصوص الحالية
- حذف النصوص غير المرغوبة
- معاينة النصوص قبل الحفظ

#### تبويب الإعدادات العامة
- تعديل اسم المسجد
- تحديد الموقع الجغرافي (خط العرض والطول)
- إعدادات العرض والتصميم

## 📁 هيكل المشروع

```
mosque-prayer-display-python/
├── main.py                 # الملف الرئيسي للنسخة الكاملة
├── run_simple.py          # النسخة المبسطة للاختبار
├── build_exe.py           # أداة بناء الملف التنفيذي
├── requirements.txt       # قائمة المكتبات المطلوبة
├── install_requirements.bat # سكريبت تثبيت المتطلبات
├── README_PYTHON.md       # هذا الملف
├── src/                   # مجلد الكود المصدري
│   ├── app.py            # التطبيق الرئيسي
│   ├── components/       # مكونات الواجهة
│   │   ├── header.py     # رأس الصفحة
│   │   ├── main_content.py # المحتوى الرئيسي
│   │   ├── footer.py     # تذييل الصفحة
│   │   └── admin_panel.py # لوحة التحكم
│   ├── services/         # الخدمات
│   │   ├── prayer_times.py # حساب أوقات الصلاة
│   │   ├── weather.py    # خدمة الطقس
│   │   └── azan.py       # نظام الأذان
│   └── utils/            # الأدوات المساعدة
│       ├── arabic_text.py # معالجة النصوص العربية
│       └── config.py     # إدارة الإعدادات
├── audio/                # مجلد الملفات الصوتية
│   ├── azan_fajr.mp3    # أذان الفجر
│   ├── azan_normal.mp3  # أذان باقي الصلوات
│   └── README.txt       # تعليمات الملفات الصوتية
├── config/               # مجلد الإعدادات
│   ├── settings.json    # ملف الإعدادات الرئيسي
│   └── app.ini          # إعدادات إضافية
└── dist/                # مجلد الملفات التنفيذية (بعد البناء)
    ├── MosquePrayerDisplay_Simple.exe
    └── MosquePrayerDisplay.exe
```

## 🔧 التخصيص والإعدادات

### إضافة ملفات الأذان
1. ضع ملفات الأذان في مجلد `audio/`
2. أسماء الملفات المطلوبة:
   - `azan_fajr.mp3`: أذان الفجر
   - `azan_normal.mp3`: أذان باقي الصلوات
3. التنسيقات المدعومة: MP3, WAV, OGG

### تخصيص الموقع الجغرافي
1. افتح لوحة التحكم (مفتاح A)
2. انتقل إلى تبويب "الإعدادات العامة"
3. أدخل خط العرض وخط الطول لموقعك
4. احفظ الإعدادات

### إضافة نصوص إسلامية
1. افتح لوحة التحكم (مفتاح A)
2. انتقل إلى تبويب "النصوص الإسلامية"
3. اكتب النص الجديد في المربع
4. اضغط "إضافة النص"
5. احفظ الإعدادات

### تعديل أوقات الصلاة يدوياً
1. افتح لوحة التحكم (مفتاح A)
2. انتقل إلى تبويب "أوقات الصلاة"
3. عدّل الأوقات حسب الحاجة
4. اختبر الأذان للتأكد
5. احفظ الإعدادات

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**البرنامج لا يبدأ:**
```bash
# تحقق من إصدار Python
python --version

# تحقق من المكتبات المثبتة
pip list

# إعادة تثبيت المتطلبات
pip install -r requirements.txt --force-reinstall
```

**الأذان لا يعمل:**
- تأكد من وجود ملفات الأذان في مجلد `audio/`
- تحقق من مستوى الصوت في لوحة التحكم
- جرب اختبار الأذان من لوحة التحكم

**النصوص العربية لا تظهر بشكل صحيح:**
```bash
# تثبيت مكتبات النصوص العربية
pip install arabic-reshaper python-bidi --upgrade
```

**أوقات الصلاة غير دقيقة:**
- تحقق من الموقع الجغرافي في الإعدادات
- تأكد من اتصال الإنترنت لجلب الأوقات
- جرب تحديث الأوقات يدوياً

**مشاكل في بناء الملف التنفيذي:**
```bash
# تحديث PyInstaller
pip install pyinstaller --upgrade

# تنظيف الملفات المؤقتة
rmdir /s build dist
del *.spec

# إعادة البناء
python build_exe.py
```

## 📊 الأداء والتحسين

### نصائح لتحسين الأداء
- استخدم النسخة المبسطة للأجهزة الضعيفة
- أغلق البرامج غير الضرورية
- استخدم شاشة بدقة 1920x1080 أو أعلى
- تأكد من اتصال إنترنت مستقر

### استهلاك الموارد
- **الذاكرة**: 50-100 MB (النسخة المبسطة)
- **الذاكرة**: 100-200 MB (النسخة الكاملة)
- **المعالج**: أقل من 5% في الوضع العادي
- **الشبكة**: استخدام قليل لتحديث الأوقات والطقس

## 🔒 الأمان والخصوصية

### البيانات المحفوظة محلياً
- إعدادات التطبيق في `config/settings.json`
- كاش أوقات الصلاة في `prayer_times_cache.json`
- كاش الطقس في `weather_cache.json`

### الاتصالات الخارجية
- **AlAdhan API**: لجلب أوقات الصلاة
- **wttr.in**: لجلب معلومات الطقس
- جميع الاتصالات مشفرة (HTTPS)

### الخصوصية
- لا يتم جمع أي بيانات شخصية
- الموقع الجغرافي يُستخدم فقط لحساب الأوقات
- لا توجد إعلانات أو تتبع

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- راجع هذا الملف للتعليمات التفصيلية
- تحقق من ملف `README.md` للمعلومات العامة
- افحص console البرنامج للأخطاء التقنية

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- نظام التشغيل وإصداره
- إصدار Python المستخدم
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

### طلب ميزات جديدة
نرحب بطلبات الميزات الجديدة التي تخدم المجتمع الإسلامي.

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الحر في المساجد والمؤسسات الإسلامية.

---

**تم تطوير هذا البرنامج بعناية لخدمة المجتمع الإسلامي وتسهيل أداء الصلوات في أوقاتها المحددة.**

**جزاكم الله خيراً على استخدام هذا البرنامج في خدمة دين الله** 🕌
