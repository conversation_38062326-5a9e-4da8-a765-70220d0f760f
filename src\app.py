#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي لشاشة أوقات الصلاة
"""

import tkinter as tk
from tkinter import ttk, font
import threading
import time
from datetime import datetime, timedelta
import json
import os

from .components.header import HeaderFrame
from .components.main_content import MainContentFrame
from .components.footer import FooterFrame
from .components.admin_panel import AdminPanel
from .services.prayer_times import PrayerTimesService
from .services.weather import WeatherService
from .services.azan import AzanService
from .utils.arabic_text import ArabicTextHandler
from .utils.config import ConfigManager

class MosquePrayerApp:
    """التطبيق الرئيسي لشاشة أوقات الصلاة"""
    
    def __init__(self, root):
        self.root = root
        self.config_manager = ConfigManager()
        self.arabic_handler = ArabicTextHandler()
        
        # الخدمات
        self.prayer_service = PrayerTimesService()
        self.weather_service = WeatherService()
        self.azan_service = AzanService()
        
        # البيانات
        self.current_time = datetime.now()
        self.prayer_times = {}
        self.weather_data = {}
        self.islamic_texts = [
            "وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا",
            "إِنَّ مَعَ الْعُسْرِ يُسْرًا",
            "وَاللَّهُ خَيْرٌ حَافِظًا وَهُوَ أَرْحَمُ الرَّاحِمِينَ",
            "قال رسول الله ﷺ: الصلاة نور",
            "قال رسول الله ﷺ: خير الناس أنفعهم للناس",
            "وَبَشِّرِ الصَّابِرِينَ",
            "رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً"
        ]
        self.current_text_index = 0
        
        # حالة التطبيق
        self.is_running = True
        self.admin_panel = None
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_fonts()
        self.setup_bindings()
        
        # بدء الخدمات
        self.start_services()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.root.configure(bg='#8B0000')
        
        # إنشاء الإطار الرئيسي
        self.main_frame = tk.Frame(self.root, bg='#8B0000')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # إنشاء المكونات
        self.header_frame = HeaderFrame(self.main_frame, self)
        self.main_content_frame = MainContentFrame(self.main_frame, self)
        self.footer_frame = FooterFrame(self.main_frame, self)
        
        # ترتيب المكونات
        self.header_frame.pack(fill=tk.X, pady=(0, 20))
        self.main_content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        self.footer_frame.pack(fill=tk.X)
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # خطوط عربية
            self.fonts = {
                'title': font.Font(family='Arial Unicode MS', size=24, weight='bold'),
                'large': font.Font(family='Arial Unicode MS', size=48, weight='bold'),
                'medium': font.Font(family='Arial Unicode MS', size=18),
                'small': font.Font(family='Arial Unicode MS', size=14),
                'arabic': font.Font(family='Arial Unicode MS', size=16),
                'time': font.Font(family='Arial Unicode MS', size=72, weight='bold')
            }
        except:
            # خطوط احتياطية
            self.fonts = {
                'title': font.Font(family='Arial', size=24, weight='bold'),
                'large': font.Font(family='Arial', size=48, weight='bold'),
                'medium': font.Font(family='Arial', size=18),
                'small': font.Font(family='Arial', size=14),
                'arabic': font.Font(family='Arial', size=16),
                'time': font.Font(family='Arial', size=72, weight='bold')
            }
    
    def setup_bindings(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.root.bind('<Key-a>', self.open_admin_panel)
        self.root.bind('<Key-A>', self.open_admin_panel)
        self.root.bind('<Escape>', self.close_admin_panel)
        self.root.bind('<F11>', self.toggle_fullscreen)
        self.root.bind('<Alt-F4>', self.quit_app)
        
        # التركيز على النافذة لاستقبال الأحداث
        self.root.focus_set()
    
    def start_services(self):
        """بدء الخدمات في خيوط منفصلة"""
        # خيط تحديث الوقت
        self.time_thread = threading.Thread(target=self.update_time_loop, daemon=True)
        self.time_thread.start()
        
        # خيط تحديث النصوص الإسلامية
        self.text_thread = threading.Thread(target=self.update_text_loop, daemon=True)
        self.text_thread.start()
        
        # خيط تحديث أوقات الصلاة
        self.prayer_thread = threading.Thread(target=self.update_prayer_times_loop, daemon=True)
        self.prayer_thread.start()
        
        # خيط تحديث الطقس
        self.weather_thread = threading.Thread(target=self.update_weather_loop, daemon=True)
        self.weather_thread.start()
    
    def update_time_loop(self):
        """حلقة تحديث الوقت"""
        while self.is_running:
            try:
                self.current_time = datetime.now()
                self.root.after(0, self.update_time_display)
                time.sleep(1)
            except Exception as e:
                print(f"خطأ في تحديث الوقت: {e}")
                time.sleep(1)
    
    def update_text_loop(self):
        """حلقة تحديث النصوص الإسلامية"""
        while self.is_running:
            try:
                time.sleep(10)  # تغيير كل 10 ثوانٍ
                self.current_text_index = (self.current_text_index + 1) % len(self.islamic_texts)
                self.root.after(0, self.update_islamic_text)
            except Exception as e:
                print(f"خطأ في تحديث النصوص: {e}")
                time.sleep(10)
    
    def update_prayer_times_loop(self):
        """حلقة تحديث أوقات الصلاة"""
        while self.is_running:
            try:
                # تحديث أوقات الصلاة كل ساعة
                self.prayer_times = self.prayer_service.get_prayer_times()
                self.root.after(0, self.update_prayer_display)
                
                # جدولة الأذان
                self.azan_service.schedule_azan(self.prayer_times)
                
                time.sleep(3600)  # ساعة واحدة
            except Exception as e:
                print(f"خطأ في تحديث أوقات الصلاة: {e}")
                time.sleep(3600)
    
    def update_weather_loop(self):
        """حلقة تحديث الطقس"""
        while self.is_running:
            try:
                # تحديث الطقس كل 30 دقيقة
                self.weather_data = self.weather_service.get_weather()
                self.root.after(0, self.update_weather_display)
                time.sleep(1800)  # 30 دقيقة
            except Exception as e:
                print(f"خطأ في تحديث الطقس: {e}")
                time.sleep(1800)
    
    def update_time_display(self):
        """تحديث عرض الوقت"""
        if hasattr(self, 'main_content_frame'):
            self.main_content_frame.update_time(self.current_time)
    
    def update_islamic_text(self):
        """تحديث النص الإسلامي"""
        if hasattr(self, 'footer_frame'):
            current_text = self.islamic_texts[self.current_text_index]
            self.footer_frame.update_text(current_text)
    
    def update_prayer_display(self):
        """تحديث عرض أوقات الصلاة"""
        if hasattr(self, 'main_content_frame'):
            self.main_content_frame.update_prayer_times(self.prayer_times)
    
    def update_weather_display(self):
        """تحديث عرض الطقس"""
        if hasattr(self, 'header_frame'):
            self.header_frame.update_weather(self.weather_data)
    
    def open_admin_panel(self, event=None):
        """فتح لوحة التحكم الإدارية"""
        if self.admin_panel is None or not self.admin_panel.winfo_exists():
            self.admin_panel = AdminPanel(self.root, self)
    
    def close_admin_panel(self, event=None):
        """إغلاق لوحة التحكم الإدارية"""
        if self.admin_panel and self.admin_panel.winfo_exists():
            self.admin_panel.destroy()
            self.admin_panel = None
    
    def toggle_fullscreen(self, event=None):
        """تبديل وضع ملء الشاشة"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)
    
    def quit_app(self, event=None):
        """إنهاء التطبيق"""
        self.is_running = False
        self.azan_service.stop_all()
        self.root.quit()
        self.root.destroy()
    
    def get_font(self, font_type):
        """الحصول على خط معين"""
        return self.fonts.get(font_type, self.fonts['medium'])
