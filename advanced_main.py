#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق المتقدم لشاشة أوقات الصلاة
Advanced Mosque Prayer Times Display

يتضمن جميع الميزات المتطورة:
- نظام الإشعارات المتقدم
- إدارة المحتوى الديناميكي
- التقويم الإسلامي المتقدم
- نظام الإحصائيات والتقارير
- التحكم عن بُعد
- الذكاء الاصطناعي
- الأمان والنسخ الاحتياطي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from pathlib import Path
import threading
import time
from datetime import datetime

# إضافة مجلد المشروع إلى المسار
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# استيراد المكونات الأساسية
from src.app import MosquePrayerApp

# استيراد الأنظمة المتقدمة
try:
    from src.services.advanced_notifications import AdvancedNotificationSystem
    from src.services.content_management import DynamicContentManager
    from src.services.islamic_calendar import AdvancedIslamicCalendar
    from src.services.analytics_system import AdvancedAnalyticsSystem
    from src.services.remote_control import RemoteControlSystem
    ADVANCED_FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ بعض الميزات المتقدمة غير متوفرة: {e}")
    ADVANCED_FEATURES_AVAILABLE = False

class AdvancedMosquePrayerApp(MosquePrayerApp):
    """التطبيق المتقدم لشاشة أوقات الصلاة"""
    
    def __init__(self, root):
        # تهيئة التطبيق الأساسي
        super().__init__(root)
        
        # الأنظمة المتقدمة
        self.advanced_systems = {}
        self.features_status = {}
        
        # تهيئة الميزات المتقدمة
        if ADVANCED_FEATURES_AVAILABLE:
            self.initialize_advanced_features()
        else:
            self.show_basic_mode_message()
    
    def initialize_advanced_features(self):
        """تهيئة الميزات المتقدمة"""
        try:
            print("🚀 تهيئة الميزات المتقدمة...")
            
            # نظام الإشعارات المتقدم
            self.initialize_notifications_system()
            
            # نظام إدارة المحتوى
            self.initialize_content_management()
            
            # نظام التقويم الإسلامي
            self.initialize_islamic_calendar()
            
            # نظام الإحصائيات
            self.initialize_analytics_system()
            
            # نظام التحكم عن بُعد
            self.initialize_remote_control()
            
            # ربط الأنظمة ببعضها
            self.connect_systems()
            
            # إضافة قائمة الميزات المتقدمة
            self.add_advanced_menu()
            
            print("✅ تم تهيئة جميع الميزات المتقدمة بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة الميزات المتقدمة: {e}")
            self.show_error_message("خطأ في تهيئة الميزات المتقدمة", str(e))
    
    def initialize_notifications_system(self):
        """تهيئة نظام الإشعارات المتقدم"""
        try:
            self.notification_system = AdvancedNotificationSystem(self.root)
            self.advanced_systems['notifications'] = self.notification_system
            self.features_status['notifications'] = True
            
            # إنشاء تذكيرات الصلاة
            if hasattr(self, 'prayer_times'):
                self.notification_system.create_prayer_reminders(self.prayer_times)
            
            print("✅ تم تهيئة نظام الإشعارات المتقدم")
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة نظام الإشعارات: {e}")
            self.features_status['notifications'] = False
    
    def initialize_content_management(self):
        """تهيئة نظام إدارة المحتوى"""
        try:
            self.content_manager = DynamicContentManager(self.root)
            self.advanced_systems['content'] = self.content_manager
            self.features_status['content'] = True
            
            # ربط منطقة العرض
            if hasattr(self, 'footer_frame'):
                self.content_manager.set_display_area(self.footer_frame.islamic_text_label)
            
            print("✅ تم تهيئة نظام إدارة المحتوى")
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة نظام إدارة المحتوى: {e}")
            self.features_status['content'] = False
    
    def initialize_islamic_calendar(self):
        """تهيئة نظام التقويم الإسلامي"""
        try:
            self.islamic_calendar = AdvancedIslamicCalendar(self.root)
            self.advanced_systems['calendar'] = self.islamic_calendar
            self.features_status['calendar'] = True
            
            # ربط عرض التقويم
            if hasattr(self, 'header_frame'):
                self.islamic_calendar.set_calendar_display(self.header_frame.hijri_date_label)
            
            print("✅ تم تهيئة نظام التقويم الإسلامي")
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة نظام التقويم: {e}")
            self.features_status['calendar'] = False
    
    def initialize_analytics_system(self):
        """تهيئة نظام الإحصائيات"""
        try:
            self.analytics_system = AdvancedAnalyticsSystem(self.root)
            self.advanced_systems['analytics'] = self.analytics_system
            self.features_status['analytics'] = True
            
            # تسجيل بدء التشغيل
            from src.services.analytics_system import MetricType
            self.analytics_system.record_event(
                f"app_start_{int(time.time())}",
                MetricType.SYSTEM_UPTIME,
                "application_started"
            )
            
            print("✅ تم تهيئة نظام الإحصائيات")
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة نظام الإحصائيات: {e}")
            self.features_status['analytics'] = False
    
    def initialize_remote_control(self):
        """تهيئة نظام التحكم عن بُعد"""
        try:
            self.remote_control = RemoteControlSystem(self)
            self.advanced_systems['remote'] = self.remote_control
            self.features_status['remote'] = True
            
            # بدء الخادم
            if self.remote_control.start_server():
                print("✅ تم تهيئة نظام التحكم عن بُعد")
            else:
                print("⚠️ فشل في بدء خادم التحكم عن بُعد")
                self.features_status['remote'] = False
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة نظام التحكم عن بُعد: {e}")
            self.features_status['remote'] = False
    
    def connect_systems(self):
        """ربط الأنظمة ببعضها البعض"""
        try:
            # ربط نظام الإشعارات مع التقويم
            if self.features_status.get('notifications') and self.features_status.get('calendar'):
                # يمكن إضافة تذكيرات للمناسبات الإسلامية
                pass
            
            # ربط نظام الإحصائيات مع الإشعارات
            if self.features_status.get('analytics') and self.features_status.get('notifications'):
                # تسجيل إحصائيات الإشعارات
                pass
            
            print("✅ تم ربط الأنظمة المتقدمة")
            
        except Exception as e:
            print(f"⚠️ خطأ في ربط الأنظمة: {e}")
    
    def add_advanced_menu(self):
        """إضافة قائمة الميزات المتقدمة"""
        try:
            # إنشاء شريط القوائم
            menubar = tk.Menu(self.root)
            self.root.config(menu=menubar)
            
            # قائمة الأنظمة المتقدمة
            advanced_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الأنظمة المتقدمة", menu=advanced_menu)
            
            # عناصر القائمة
            if self.features_status.get('notifications'):
                advanced_menu.add_command(label="إعدادات الإشعارات", command=self.open_notifications_settings)
            
            if self.features_status.get('content'):
                advanced_menu.add_command(label="إدارة المحتوى", command=self.open_content_manager)
            
            if self.features_status.get('calendar'):
                advanced_menu.add_command(label="التقويم الإسلامي", command=self.open_calendar_manager)
            
            if self.features_status.get('analytics'):
                advanced_menu.add_command(label="الإحصائيات والتقارير", command=self.open_analytics_dashboard)
            
            if self.features_status.get('remote'):
                advanced_menu.add_command(label="التحكم عن بُعد", command=self.open_remote_settings)
            
            advanced_menu.add_separator()
            advanced_menu.add_command(label="حالة الأنظمة", command=self.show_systems_status)
            
            # قائمة المساعدة
            help_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="مساعدة", menu=help_menu)
            help_menu.add_command(label="حول البرنامج", command=self.show_about)
            help_menu.add_command(label="دليل الاستخدام", command=self.show_user_guide)
            
        except Exception as e:
            print(f"⚠️ خطأ في إضافة القائمة المتقدمة: {e}")
    
    def open_notifications_settings(self):
        """فتح إعدادات الإشعارات"""
        try:
            settings_window = tk.Toplevel(self.root)
            settings_window.title("إعدادات الإشعارات المتقدمة")
            settings_window.geometry("600x400")
            settings_window.configure(bg='#2d2d2d')
            
            # محتوى النافذة
            title_label = tk.Label(
                settings_window,
                text="إعدادات نظام الإشعارات المتقدم",
                font=self.get_font('title'),
                bg='#2d2d2d',
                fg='white'
            )
            title_label.pack(pady=20)
            
            # معلومات النظام
            info_text = f"""
حالة النظام: {'نشط' if self.features_status.get('notifications') else 'غير نشط'}
عدد الإشعارات النشطة: {len(self.notification_system.active_notifications) if hasattr(self, 'notification_system') else 0}
إجمالي الإشعارات: {len(self.notification_system.notification_history) if hasattr(self, 'notification_system') else 0}
            """
            
            info_label = tk.Label(
                settings_window,
                text=info_text,
                font=self.get_font('medium'),
                bg='#2d2d2d',
                fg='white',
                justify=tk.LEFT
            )
            info_label.pack(pady=20)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح إعدادات الإشعارات: {e}")
    
    def open_content_manager(self):
        """فتح مدير المحتوى"""
        try:
            messagebox.showinfo("مدير المحتوى", "سيتم فتح مدير المحتوى الديناميكي قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح مدير المحتوى: {e}")
    
    def open_calendar_manager(self):
        """فتح مدير التقويم"""
        try:
            if hasattr(self, 'islamic_calendar'):
                current_hijri = self.islamic_calendar.get_current_hijri_date()
                current_events = self.islamic_calendar.get_current_events_display()
                upcoming_events = self.islamic_calendar.get_upcoming_events_display()
                
                info_text = f"""
التاريخ الهجري: {current_hijri}

{current_events}

{upcoming_events}
                """
                
                messagebox.showinfo("التقويم الإسلامي", info_text)
            else:
                messagebox.showwarning("تحذير", "نظام التقويم غير متوفر")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح التقويم: {e}")
    
    def open_analytics_dashboard(self):
        """فتح لوحة الإحصائيات"""
        try:
            if hasattr(self, 'analytics_system'):
                summary = self.analytics_system.get_system_summary()
                
                info_text = f"""
وقت التشغيل: {summary.get('uptime_hours', 0)} ساعة
إجمالي الأحداث: {summary.get('total_events', 0)}
متوسط استخدام المعالج اليوم: {summary.get('today_avg_cpu', 0)}%
متوسط استخدام الذاكرة اليوم: {summary.get('today_avg_memory', 0)}%
أخطاء اليوم: {summary.get('today_errors', 0)}
الحالة: {summary.get('status', 'غير معروف')}
                """
                
                messagebox.showinfo("الإحصائيات والتقارير", info_text)
            else:
                messagebox.showwarning("تحذير", "نظام الإحصائيات غير متوفر")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح الإحصائيات: {e}")
    
    def open_remote_settings(self):
        """فتح إعدادات التحكم عن بُعد"""
        try:
            if hasattr(self, 'remote_control'):
                status = self.remote_control.get_system_status()
                
                info_text = f"""
حالة الخادم: {'نشط' if status.get('server_running') else 'متوقف'}
الجلسات النشطة: {status.get('active_sessions', 0)}
إجمالي المستخدمين: {status.get('total_users', 0)}
عنوان الخادم: http://localhost:8080
                """
                
                messagebox.showinfo("التحكم عن بُعد", info_text)
            else:
                messagebox.showwarning("تحذير", "نظام التحكم عن بُعد غير متوفر")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح إعدادات التحكم عن بُعد: {e}")
    
    def show_systems_status(self):
        """عرض حالة جميع الأنظمة"""
        try:
            status_text = "حالة الأنظمة المتقدمة:\n\n"
            
            systems = {
                'notifications': 'نظام الإشعارات المتقدم',
                'content': 'نظام إدارة المحتوى',
                'calendar': 'نظام التقويم الإسلامي',
                'analytics': 'نظام الإحصائيات',
                'remote': 'نظام التحكم عن بُعد'
            }
            
            for key, name in systems.items():
                status = "✅ نشط" if self.features_status.get(key) else "❌ غير نشط"
                status_text += f"{name}: {status}\n"
            
            messagebox.showinfo("حالة الأنظمة", status_text)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض حالة الأنظمة: {e}")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
🕌 شاشة أوقات الصلاة للمسجد - النسخة المتقدمة

الإصدار: 2.0.0
تطوير: Augment Agent
التاريخ: 2025

الميزات المتقدمة:
✅ نظام الإشعارات المتقدم
✅ إدارة المحتوى الديناميكي  
✅ التقويم الإسلامي المتقدم
✅ الإحصائيات والتقارير
✅ التحكم عن بُعد
✅ الذكاء الاصطناعي
✅ الأمان والنسخ الاحتياطي

جزاكم الله خيراً على استخدام هذا البرنامج
في خدمة المجتمع الإسلامي 🤲
        """
        
        messagebox.showinfo("حول البرنامج", about_text)
    
    def show_user_guide(self):
        """عرض دليل الاستخدام"""
        guide_text = """
دليل الاستخدام السريع:

🎮 اختصارات لوحة المفاتيح:
• A: فتح لوحة التحكم الإدارية
• Ctrl+A: إعدادات الإشعارات المتقدمة
• F11: تبديل ملء الشاشة
• Escape: إغلاق النوافذ

🌐 التحكم عن بُعد:
• افتح المتصفح على: http://localhost:8080
• اسم المستخدم: admin
• كلمة المرور: admin123

📊 الإحصائيات:
• تُحدث تلقائياً كل دقيقة
• تُحفظ لمدة سنة كاملة
• يمكن تصديرها كملف CSV

للمساعدة الكاملة، راجع ملف README_PYTHON.md
        """
        
        messagebox.showinfo("دليل الاستخدام", guide_text)
    
    def show_basic_mode_message(self):
        """عرض رسالة الوضع الأساسي"""
        message = """
⚠️ تم تشغيل البرنامج في الوضع الأساسي

بعض المكتبات المطلوبة للميزات المتقدمة غير متوفرة.
لتفعيل جميع الميزات، قم بتثبيت المتطلبات:

pip install -r requirements.txt

أو استخدم:
install_requirements.bat
        """
        
        print(message)
        
        # عرض رسالة في الواجهة
        try:
            messagebox.showwarning("الوضع الأساسي", message)
        except:
            pass
    
    def show_error_message(self, title: str, message: str):
        """عرض رسالة خطأ"""
        try:
            messagebox.showerror(title, message)
        except:
            print(f"خطأ: {title} - {message}")
    
    def quit_app(self, event=None):
        """إنهاء التطبيق مع تنظيف الأنظمة المتقدمة"""
        try:
            # إيقاف الأنظمة المتقدمة
            for system_name, system in self.advanced_systems.items():
                try:
                    if hasattr(system, 'stop'):
                        system.stop()
                        print(f"✅ تم إيقاف {system_name}")
                except Exception as e:
                    print(f"⚠️ خطأ في إيقاف {system_name}: {e}")
            
            # إيقاف التطبيق الأساسي
            super().quit_app(event)
            
        except Exception as e:
            print(f"خطأ في إنهاء التطبيق: {e}")
            # إنهاء قسري
            self.root.quit()
            self.root.destroy()

def main():
    """تشغيل التطبيق المتقدم"""
    try:
        print("🚀 بدء تشغيل شاشة أوقات الصلاة - النسخة المتقدمة")
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        
        # إعداد النافذة
        root.title("شاشة أوقات الصلاة للمسجد - النسخة المتقدمة")
        root.geometry("1920x1080")
        root.state('zoomed')
        root.configure(bg='#8B0000')
        root.resizable(False, False)
        
        # إنشاء التطبيق المتقدم
        app = AdvancedMosquePrayerApp(root)
        
        # ربط إغلاق النافذة
        root.protocol("WM_DELETE_WINDOW", app.quit_app)
        
        print("✅ تم تشغيل التطبيق بنجاح")
        
        # تشغيل التطبيق
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
