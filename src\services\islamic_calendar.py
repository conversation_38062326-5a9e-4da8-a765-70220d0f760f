#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقويم الإسلامي المتقدم
Advanced Islamic Calendar System
"""

import tkinter as tk
from tkinter import ttk
import json
from datetime import datetime, timed<PERSON>ta
from hijri_converter import Hi<PERSON><PERSON>, <PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
from enum import Enum
import requests
import threading

class EventType(Enum):
    """أنواع الأحداث الإسلامية"""
    RELIGIOUS_HOLIDAY = "religious_holiday"
    PROPHET_BIRTHDAY = "prophet_birthday"
    HIJRI_NEW_YEAR = "hijri_new_year"
    RAMADAN = "ramadan"
    HAJJ = "hajj"
    FRIDAY_PRAYER = "friday_prayer"
    EID = "eid"
    NIGHT_OF_POWER = "night_of_power"
    ASHURA = "ashura"
    MAWLID = "mawlid"
    ISRA_MIRAJ = "isra_miraj"
    CUSTOM_EVENT = "custom_event"

class EventPriority(Enum):
    """أولوية الأحداث"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class IslamicEvent:
    """حدث إسلامي"""
    
    def __init__(self,
                 event_id: str,
                 title: str,
                 description: str,
                 event_type: EventType,
                 hijri_date: Tuple[int, int, int],  # (year, month, day)
                 duration_days: int = 1,
                 priority: EventPriority = EventPriority.NORMAL,
                 reminder_days: List[int] = None,
                 custom_message: str = "",
                 is_recurring: bool = True):
        
        self.id = event_id
        self.title = title
        self.description = description
        self.type = event_type
        self.hijri_date = hijri_date
        self.duration_days = duration_days
        self.priority = priority
        self.reminder_days = reminder_days or [7, 3, 1]
        self.custom_message = custom_message
        self.is_recurring = is_recurring
        self.created_at = datetime.now()

class AdvancedIslamicCalendar:
    """نظام التقويم الإسلامي المتقدم"""
    
    def __init__(self, parent_window):
        self.parent = parent_window
        self.events = {}
        self.current_events = []
        self.upcoming_events = []
        
        # إعدادات النظام
        self.settings = self.load_settings()
        
        # مكونات الواجهة
        self.calendar_display = None
        self.event_display = None
        
        # خيوط العمل
        self.calendar_thread = None
        self.is_running = True
        
        self.initialize_calendar_system()
        self.load_islamic_events()
        self.start_calendar_updates()
    
    def load_settings(self) -> Dict:
        """تحميل إعدادات التقويم"""
        default_settings = {
            "enabled": True,
            "show_hijri_date": True,
            "show_gregorian_date": True,
            "show_upcoming_events": True,
            "reminder_enabled": True,
            "auto_update": True,
            "update_interval": 3600,  # ثانية
            "display_duration": 30,  # ثانية لعرض الحدث
            "max_upcoming_events": 5,
            "event_sources": {
                "local": True,
                "online": True,
                "custom": True
            },
            "notification_settings": {
                "visual": True,
                "audio": True,
                "popup": True
            }
        }
        
        try:
            with open('config/calendar_settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
        except:
            return default_settings
    
    def save_settings(self):
        """حفظ إعدادات التقويم"""
        try:
            with open('config/calendar_settings.json', 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات التقويم: {e}")
    
    def initialize_calendar_system(self):
        """تهيئة نظام التقويم"""
        try:
            # إنشاء مجلد البيانات
            import os
            os.makedirs('data/calendar', exist_ok=True)
            
            print("✅ تم تهيئة نظام التقويم الإسلامي")
            
        except Exception as e:
            print(f"⚠️ خطأ في تهيئة نظام التقويم: {e}")
    
    def load_islamic_events(self):
        """تحميل الأحداث الإسلامية"""
        try:
            # تحميل الأحداث المحلية
            self.load_local_events()
            
            # تحميل الأحداث المخصصة
            self.load_custom_events()
            
            # تحميل الأحداث من الإنترنت (اختياري)
            if self.settings["event_sources"]["online"]:
                threading.Thread(target=self.load_online_events, daemon=True).start()
            
            print(f"✅ تم تحميل {len(self.events)} حدث إسلامي")
            
        except Exception as e:
            print(f"خطأ في تحميل الأحداث الإسلامية: {e}")
    
    def load_local_events(self):
        """تحميل الأحداث المحلية المبرمجة مسبقاً"""
        # الأحداث الإسلامية الثابتة
        fixed_events = [
            # رأس السنة الهجرية
            IslamicEvent(
                "hijri_new_year",
                "رأس السنة الهجرية",
                "بداية العام الهجري الجديد",
                EventType.HIJRI_NEW_YEAR,
                (0, 1, 1),  # 1 محرم (السنة تتغير)
                1,
                EventPriority.HIGH,
                [7, 3, 1]
            ),
            
            # عاشوراء
            IslamicEvent(
                "ashura",
                "يوم عاشوراء",
                "اليوم العاشر من محرم - يوم صيام مستحب",
                EventType.ASHURA,
                (0, 1, 10),  # 10 محرم
                1,
                EventPriority.HIGH,
                [3, 1]
            ),
            
            # المولد النبوي
            IslamicEvent(
                "mawlid",
                "المولد النبوي الشريف",
                "ذكرى مولد النبي محمد صلى الله عليه وسلم",
                EventType.MAWLID,
                (0, 3, 12),  # 12 ربيع الأول
                1,
                EventPriority.CRITICAL,
                [7, 3, 1]
            ),
            
            # الإسراء والمعراج
            IslamicEvent(
                "isra_miraj",
                "ليلة الإسراء والمعراج",
                "ذكرى رحلة النبي الليلية من المسجد الحرام إلى المسجد الأقصى",
                EventType.ISRA_MIRAJ,
                (0, 7, 27),  # 27 رجب
                1,
                EventPriority.HIGH,
                [7, 3, 1]
            ),
            
            # ليلة النصف من شعبان
            IslamicEvent(
                "laylat_nisf_shaban",
                "ليلة النصف من شعبان",
                "ليلة مباركة في منتصف شعبان",
                EventType.RELIGIOUS_HOLIDAY,
                (0, 8, 15),  # 15 شعبان
                1,
                EventPriority.NORMAL,
                [3, 1]
            ),
            
            # بداية رمضان
            IslamicEvent(
                "ramadan_start",
                "بداية شهر رمضان المبارك",
                "أول أيام الصيام في الشهر الكريم",
                EventType.RAMADAN,
                (0, 9, 1),  # 1 رمضان
                30,
                EventPriority.CRITICAL,
                [7, 3, 1]
            ),
            
            # ليلة القدر
            IslamicEvent(
                "laylat_qadr",
                "ليلة القدر",
                "ليلة خير من ألف شهر - في العشر الأواخر من رمضان",
                EventType.NIGHT_OF_POWER,
                (0, 9, 27),  # 27 رمضان (تقريبي)
                1,
                EventPriority.CRITICAL,
                [7, 3, 1]
            ),
            
            # عيد الفطر
            IslamicEvent(
                "eid_fitr",
                "عيد الفطر المبارك",
                "عيد انتهاء شهر رمضان",
                EventType.EID,
                (0, 10, 1),  # 1 شوال
                3,
                EventPriority.CRITICAL,
                [7, 3, 1]
            ),
            
            # يوم عرفة
            IslamicEvent(
                "arafat_day",
                "يوم عرفة",
                "اليوم التاسع من ذي الحجة - يوم الحج الأكبر",
                EventType.HAJJ,
                (0, 12, 9),  # 9 ذو الحجة
                1,
                EventPriority.CRITICAL,
                [7, 3, 1]
            ),
            
            # عيد الأضحى
            IslamicEvent(
                "eid_adha",
                "عيد الأضحى المبارك",
                "عيد الحج وذكرى فداء إبراهيم عليه السلام",
                EventType.EID,
                (0, 12, 10),  # 10 ذو الحجة
                4,
                EventPriority.CRITICAL,
                [7, 3, 1]
            )
        ]
        
        # إضافة الأحداث للقاموس
        for event in fixed_events:
            self.events[event.id] = event
    
    def load_custom_events(self):
        """تحميل الأحداث المخصصة"""
        try:
            with open('data/calendar/custom_events.json', 'r', encoding='utf-8') as f:
                custom_data = json.load(f)
                
                for event_data in custom_data:
                    event = IslamicEvent(
                        event_data["id"],
                        event_data["title"],
                        event_data["description"],
                        EventType(event_data["type"]),
                        tuple(event_data["hijri_date"]),
                        event_data.get("duration_days", 1),
                        EventPriority(event_data.get("priority", 2)),
                        event_data.get("reminder_days", [1]),
                        event_data.get("custom_message", ""),
                        event_data.get("is_recurring", True)
                    )
                    self.events[event.id] = event
                    
        except FileNotFoundError:
            # إنشاء ملف فارغ للأحداث المخصصة
            with open('data/calendar/custom_events.json', 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في تحميل الأحداث المخصصة: {e}")
    
    def load_online_events(self):
        """تحميل الأحداث من الإنترنت"""
        try:
            # يمكن ربطها بـ API للأحداث الإسلامية
            # مثل Islamic Calendar API أو مواقع التقويم الإسلامي
            
            # مثال على API وهمي
            # response = requests.get("https://api.islamic-calendar.com/events")
            # if response.status_code == 200:
            #     online_events = response.json()
            #     # معالجة البيانات وإضافتها
            
            print("📡 تم محاولة تحميل الأحداث من الإنترنت")
            
        except Exception as e:
            print(f"خطأ في تحميل الأحداث من الإنترنت: {e}")
    
    def start_calendar_updates(self):
        """بدء تحديثات التقويم"""
        self.calendar_thread = threading.Thread(
            target=self.calendar_update_loop,
            daemon=True
        )
        self.calendar_thread.start()
    
    def calendar_update_loop(self):
        """حلقة تحديث التقويم"""
        while self.is_running:
            try:
                if self.settings.get("enabled", True):
                    self.update_current_events()
                    self.update_upcoming_events()
                    self.check_event_reminders()
                
                # تحديث كل ساعة
                import time
                time.sleep(self.settings.get("update_interval", 3600))
                
            except Exception as e:
                print(f"خطأ في تحديث التقويم: {e}")
                import time
                time.sleep(300)  # 5 دقائق في حالة الخطأ
    
    def update_current_events(self):
        """تحديث الأحداث الحالية"""
        try:
            today = datetime.now()
            today_hijri = Gregorian(today.year, today.month, today.day).to_hijri()
            
            current_events = []
            
            for event in self.events.values():
                if self.is_event_active(event, today_hijri):
                    current_events.append(event)
            
            # ترتيب حسب الأولوية
            current_events.sort(key=lambda x: x.priority.value, reverse=True)
            
            self.current_events = current_events
            
        except Exception as e:
            print(f"خطأ في تحديث الأحداث الحالية: {e}")
    
    def update_upcoming_events(self):
        """تحديث الأحداث القادمة"""
        try:
            today = datetime.now()
            today_hijri = Gregorian(today.year, today.month, today.day).to_hijri()
            
            upcoming_events = []
            
            for event in self.events.values():
                days_until = self.days_until_event(event, today_hijri)
                if 0 < days_until <= 30:  # الأحداث في الشهر القادم
                    upcoming_events.append((event, days_until))
            
            # ترتيب حسب القرب الزمني
            upcoming_events.sort(key=lambda x: x[1])
            
            # أخذ أول N أحداث
            max_events = self.settings.get("max_upcoming_events", 5)
            self.upcoming_events = upcoming_events[:max_events]
            
        except Exception as e:
            print(f"خطأ في تحديث الأحداث القادمة: {e}")
    
    def is_event_active(self, event: IslamicEvent, current_hijri: Hijri) -> bool:
        """التحقق من نشاط الحدث"""
        try:
            # تحديث سنة الحدث للسنة الحالية
            event_year = current_hijri.year
            event_hijri = Hijri(event_year, event.hijri_date[1], event.hijri_date[2])
            
            # التحقق من التاريخ
            if event.duration_days == 1:
                return (current_hijri.year == event_hijri.year and
                        current_hijri.month == event_hijri.month and
                        current_hijri.day == event_hijri.day)
            else:
                # حدث متعدد الأيام
                end_date = event_hijri + timedelta(days=event.duration_days - 1)
                return event_hijri <= current_hijri <= end_date
            
        except Exception as e:
            print(f"خطأ في التحقق من نشاط الحدث: {e}")
            return False
    
    def days_until_event(self, event: IslamicEvent, current_hijri: Hijri) -> int:
        """حساب الأيام المتبقية للحدث"""
        try:
            # تحديد سنة الحدث
            event_year = current_hijri.year
            event_hijri = Hijri(event_year, event.hijri_date[1], event.hijri_date[2])
            
            # إذا كان الحدث قد مضى هذا العام، احسب للعام القادم
            if event_hijri < current_hijri:
                event_hijri = Hijri(event_year + 1, event.hijri_date[1], event.hijri_date[2])
            
            # تحويل للتاريخ الميلادي للحساب
            current_gregorian = current_hijri.to_gregorian()
            event_gregorian = event_hijri.to_gregorian()
            
            # حساب الفرق
            delta = event_gregorian - current_gregorian
            return delta.days
            
        except Exception as e:
            print(f"خطأ في حساب الأيام المتبقية: {e}")
            return -1
    
    def check_event_reminders(self):
        """فحص تذكيرات الأحداث"""
        try:
            if not self.settings.get("reminder_enabled", True):
                return
            
            today = datetime.now()
            today_hijri = Gregorian(today.year, today.month, today.day).to_hijri()
            
            for event in self.events.values():
                days_until = self.days_until_event(event, today_hijri)
                
                if days_until in event.reminder_days:
                    self.send_event_reminder(event, days_until)
            
        except Exception as e:
            print(f"خطأ في فحص التذكيرات: {e}")
    
    def send_event_reminder(self, event: IslamicEvent, days_until: int):
        """إرسال تذكير الحدث"""
        try:
            # إنشاء رسالة التذكير
            if days_until == 0:
                message = f"اليوم هو {event.title}"
            elif days_until == 1:
                message = f"غداً: {event.title}"
            else:
                message = f"خلال {days_until} أيام: {event.title}"
            
            # إضافة الرسالة المخصصة إن وجدت
            if event.custom_message:
                message += f"\n{event.custom_message}"
            
            # إرسال الإشعار (يحتاج ربط مع نظام الإشعارات)
            print(f"🔔 تذكير: {message}")
            
            # يمكن ربطها مع نظام الإشعارات المتقدم
            # self.notification_system.add_notification(...)
            
        except Exception as e:
            print(f"خطأ في إرسال التذكير: {e}")
    
    def get_current_hijri_date(self) -> str:
        """الحصول على التاريخ الهجري الحالي"""
        try:
            today = datetime.now()
            hijri_date = Gregorian(today.year, today.month, today.day).to_hijri()
            
            # أسماء الشهور الهجرية
            hijri_months = [
                'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني',
                'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان',
                'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
            ]
            
            month_name = hijri_months[hijri_date.month - 1]
            return f"{hijri_date.day} {month_name} {hijri_date.year} هـ"
            
        except Exception as e:
            print(f"خطأ في الحصول على التاريخ الهجري: {e}")
            return "التاريخ الهجري غير متوفر"
    
    def get_current_events_display(self) -> str:
        """الحصول على نص عرض الأحداث الحالية"""
        try:
            if not self.current_events:
                return "لا توجد مناسبات إسلامية اليوم"
            
            if len(self.current_events) == 1:
                event = self.current_events[0]
                return f"🌙 {event.title}"
            else:
                events_text = "🌙 المناسبات اليوم:\n"
                for event in self.current_events[:3]:  # أول 3 أحداث
                    events_text += f"• {event.title}\n"
                return events_text.strip()
            
        except Exception as e:
            print(f"خطأ في عرض الأحداث الحالية: {e}")
            return "خطأ في عرض الأحداث"
    
    def get_upcoming_events_display(self) -> str:
        """الحصول على نص عرض الأحداث القادمة"""
        try:
            if not self.upcoming_events:
                return "لا توجد مناسبات قادمة"
            
            events_text = "📅 المناسبات القادمة:\n"
            for event, days_until in self.upcoming_events[:3]:
                if days_until == 1:
                    events_text += f"• غداً: {event.title}\n"
                else:
                    events_text += f"• خلال {days_until} أيام: {event.title}\n"
            
            return events_text.strip()
            
        except Exception as e:
            print(f"خطأ في عرض الأحداث القادمة: {e}")
            return "خطأ في عرض الأحداث القادمة"
    
    def add_custom_event(self, event: IslamicEvent) -> bool:
        """إضافة حدث مخصص"""
        try:
            # إضافة للقاموس
            self.events[event.id] = event
            
            # حفظ في ملف الأحداث المخصصة
            self.save_custom_events()
            
            print(f"✅ تم إضافة حدث مخصص: {event.title}")
            return True
            
        except Exception as e:
            print(f"خطأ في إضافة الحدث المخصص: {e}")
            return False
    
    def save_custom_events(self):
        """حفظ الأحداث المخصصة"""
        try:
            custom_events = []
            
            for event in self.events.values():
                if event.type == EventType.CUSTOM_EVENT:
                    custom_events.append({
                        "id": event.id,
                        "title": event.title,
                        "description": event.description,
                        "type": event.type.value,
                        "hijri_date": list(event.hijri_date),
                        "duration_days": event.duration_days,
                        "priority": event.priority.value,
                        "reminder_days": event.reminder_days,
                        "custom_message": event.custom_message,
                        "is_recurring": event.is_recurring
                    })
            
            with open('data/calendar/custom_events.json', 'w', encoding='utf-8') as f:
                json.dump(custom_events, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ الأحداث المخصصة: {e}")
    
    def set_calendar_display(self, display_widget):
        """تعيين عنصر عرض التقويم"""
        self.calendar_display = display_widget
    
    def set_event_display(self, display_widget):
        """تعيين عنصر عرض الأحداث"""
        self.event_display = display_widget
    
    def update_display(self):
        """تحديث عرض التقويم"""
        try:
            if self.calendar_display:
                hijri_date = self.get_current_hijri_date()
                self.calendar_display.config(text=hijri_date)
            
            if self.event_display:
                current_events = self.get_current_events_display()
                self.event_display.config(text=current_events)
                
        except Exception as e:
            print(f"خطأ في تحديث عرض التقويم: {e}")
    
    def stop(self):
        """إيقاف نظام التقويم"""
        self.is_running = False
        self.save_custom_events()
        print("🛑 تم إيقاف نظام التقويم الإسلامي")
