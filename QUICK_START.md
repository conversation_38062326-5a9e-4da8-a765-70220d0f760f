# دليل التشغيل السريع
## Quick Start Guide

## 🚀 التشغيل الفوري

### للمعاينة السريعة:
افتح الملف `demo.html` في أي متصفح ويب حديث لمشاهدة النسخة التجريبية.

### للتطوير الكامل:
```bash
# تثبيت Node.js أولاً من https://nodejs.org
npm install
npm run dev
```

## ✨ المميزات المتوفرة

### ✅ تم تطويرها:
- [x] تصميم الواجهة الرئيسية بالخلفية الحمراء المزخرفة
- [x] عرض الوقت الحالي والتاريخ الهجري/الميلادي
- [x] عرض أوقات الصلاة الستة مع أوقات الإقامة
- [x] دائرة العد التنازلي للصلاة القادمة
- [x] عرض معلومات الطقس وعلم الدولة
- [x] النصوص الإسلامية المتناوبة
- [x] نظام الأذان التلقائي
- [x] لوحة التحكم الإدارية الكاملة
- [x] دعم اللغة العربية والاتجاه RTL
- [x] تصميم متجاوب للشاشات المختلفة

### 🎯 الميزات الرئيسية:

1. **الواجهة الرئيسية**:
   - خلفية حمراء متدرجة مع أنماط إسلامية
   - ساعة رقمية كبيرة في المنتصف
   - عرض التاريخ الهجري والميلادي
   - معلومات الطقس مع علم الدولة

2. **أوقات الصلاة**:
   - عرض الأوقات الستة: الفجر، الشروق، الظهر، العصر، المغرب، العشاء
   - وقت الأذان ووقت الإقامة لكل صلاة
   - حساب تلقائي بناءً على الموقع الجغرافي

3. **العد التنازلي**:
   - دائرة تعرض الوقت المتبقي للصلاة القادمة
   - تحديث مباشر كل ثانية

4. **النصوص الإسلامية**:
   - آيات قرآنية وأحاديث نبوية
   - تغيير تلقائي كل 10 ثوانٍ
   - خط عربي جميل

5. **نظام الأذان**:
   - تشغيل تلقائي في أوقات الصلاة
   - تحكم في مستوى الصوت
   - دعم ملفات صوتية مختلفة

6. **لوحة التحكم** (اضغط 'A'):
   - تعديل أوقات الصلاة والإقامة
   - إعدادات الصوت والأذان
   - إدارة النصوص الإسلامية
   - تخصيص اسم المسجد

## 🎮 كيفية الاستخدام

### التشغيل العادي:
- الشاشة تعمل تلقائياً
- الوقت والتاريخ يتحدثان مباشرة
- النصوص تتغير كل 10 ثوانٍ

### لوحة التحكم:
- اضغط مفتاح `A` لفتح لوحة التحكم
- اضغط `Escape` للإغلاق
- احفظ الإعدادات بعد التعديل

## 📁 ملفات المشروع

```
taerk/
├── demo.html              # النسخة التجريبية (افتحها مباشرة)
├── package.json           # إعدادات المشروع
├── index.html            # الصفحة الرئيسية
├── src/                  # مجلد الكود المصدري
│   ├── App.jsx          # المكون الرئيسي
│   ├── components/      # مكونات الواجهة
│   ├── services/        # خدمات البيانات
│   └── utils/           # أدوات مساعدة
├── public/              # الملفات العامة
│   └── audio/           # ملفات الأذان
└── README.md            # الوثائق الكاملة
```

## 🔧 التخصيص السريع

### تغيير اسم المسجد:
افتح لوحة التحكم (A) → الإعدادات العامة

### تعديل أوقات الصلاة:
افتح لوحة التحكم (A) → أوقات الصلاة

### إضافة نصوص إسلامية:
افتح لوحة التحكم (A) → النصوص الإسلامية

### تعديل مستوى الصوت:
افتح لوحة التحكم (A) → إعدادات الصوت

## 🌐 متطلبات التشغيل

- متصفح ويب حديث (Chrome, Firefox, Edge, Safari)
- دقة شاشة: 1920x1080 أو أعلى (مُحسَّن للشاشات الكبيرة)
- اتصال إنترنت (لجلب أوقات الصلاة والطقس)

## 📞 الدعم

للمساعدة أو الاستفسارات:
- راجع ملف README.md للتفاصيل الكاملة
- تحقق من console المتصفح للأخطاء
- تأكد من السماح للمتصفح بالوصول للموقع الجغرافي

---

**البرنامج جاهز للاستخدام في المساجد والمؤسسات الإسلامية** 🕌
