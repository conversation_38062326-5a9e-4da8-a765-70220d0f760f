#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء ملف تنفيذي للبرنامج
"""

import os
import sys
import subprocess
from pathlib import Path

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('audio', 'audio'),
        ('config', 'config'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'pygame',
        'pyttsx3',
        'requests',
        'arabic_reshaper',
        'bidi',
        'hijri_converter',
        'PIL',
        'geopy',
        'schedule',
        'configparser'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MosquePrayerDisplay',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'
)
'''
    
    with open('mosque_app.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("تم إنشاء ملف mosque_app.spec")

def create_simple_spec():
    """إنشاء ملف .spec للنسخة المبسطة"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['run_simple.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MosquePrayerDisplay_Simple',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('simple_app.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("تم إنشاء ملف simple_app.spec")

def create_icon():
    """إنشاء أيقونة للتطبيق"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # إنشاء صورة 256x256
        img = Image.new('RGBA', (256, 256), (139, 0, 0, 255))  # خلفية حمراء
        draw = ImageDraw.Draw(img)
        
        # رسم دائرة
        draw.ellipse([20, 20, 236, 236], fill=(220, 20, 60, 255), outline=(255, 255, 255, 255), width=4)
        
        # إضافة رمز المسجد (نص)
        try:
            # محاولة استخدام خط عربي
            font = ImageFont.truetype("arial.ttf", 80)
        except:
            font = ImageFont.load_default()
        
        # رسم رمز المسجد
        text = "🕌"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (256 - text_width) // 2
        y = (256 - text_height) // 2
        
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        
        # حفظ كـ ICO
        img.save('icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("تم إنشاء ملف icon.ico")
        
    except ImportError:
        print("تحذير: لم يتم العثور على مكتبة PIL، لن يتم إنشاء الأيقونة")
    except Exception as e:
        print(f"خطأ في إنشاء الأيقونة: {e}")

def build_app(app_type="simple"):
    """بناء التطبيق"""
    print(f"بدء بناء التطبيق ({app_type})...")
    
    try:
        # التحقق من وجود PyInstaller
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("خطأ: PyInstaller غير مثبت")
            print("قم بتثبيته باستخدام: pip install pyinstaller")
            return False
        
        print(f"PyInstaller version: {result.stdout.strip()}")
        
        # إنشاء الأيقونة
        create_icon()
        
        if app_type == "simple":
            # بناء النسخة المبسطة
            create_simple_spec()
            cmd = ['pyinstaller', '--onefile', '--windowed', 'simple_app.spec']
        else:
            # بناء النسخة الكاملة
            create_spec_file()
            cmd = ['pyinstaller', '--onefile', '--windowed', 'mosque_app.spec']
        
        print(f"تشغيل الأمر: {' '.join(cmd)}")
        
        # تشغيل PyInstaller
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء التطبيق بنجاح!")
            print("📁 الملف التنفيذي موجود في مجلد 'dist'")
            
            # عرض معلومات الملف المبني
            dist_dir = Path("dist")
            if dist_dir.exists():
                exe_files = list(dist_dir.glob("*.exe"))
                for exe_file in exe_files:
                    size_mb = exe_file.stat().st_size / (1024 * 1024)
                    print(f"📄 {exe_file.name} - {size_mb:.1f} MB")
            
            return True
        else:
            print("❌ فشل في بناء التطبيق")
            print("خطأ:", result.stderr)
            return False
            
    except FileNotFoundError:
        print("خطأ: PyInstaller غير موجود")
        print("قم بتثبيته باستخدام: pip install pyinstaller")
        return False
    except Exception as e:
        print(f"خطأ غير متوقع: {e}")
        return False

def create_installer_script():
    """إنشاء سكريبت تثبيت المتطلبات"""
    install_script = '''@echo off
echo تثبيت متطلبات برنامج شاشة أوقات الصلاة
echo =====================================

echo تثبيت Python packages...
pip install --upgrade pip
pip install tkinter
pip install requests
pip install pygame
pip install Pillow
pip install python-bidi
pip install arabic-reshaper
pip install hijri-converter
pip install geopy
pip install pyttsx3
pip install schedule
pip install configparser
pip install pyinstaller

echo.
echo تم تثبيت جميع المتطلبات بنجاح!
echo يمكنك الآن تشغيل البرنامج باستخدام:
echo python run_simple.py
echo.
echo أو بناء ملف تنفيذي باستخدام:
echo python build_exe.py
echo.
pause
'''
    
    with open('install_requirements.bat', 'w', encoding='utf-8') as f:
        f.write(install_script)
    
    print("تم إنشاء ملف install_requirements.bat")

def main():
    """الدالة الرئيسية"""
    print("🕌 أداة بناء برنامج شاشة أوقات الصلاة")
    print("=" * 50)
    
    # إنشاء سكريبت التثبيت
    create_installer_script()
    
    # اختيار نوع البناء
    print("\nاختر نوع البناء:")
    print("1. النسخة المبسطة (بدون مكتبات إضافية)")
    print("2. النسخة الكاملة (مع جميع الميزات)")
    print("3. كلاهما")
    
    try:
        choice = input("\nاختيارك (1/2/3): ").strip()
        
        if choice == "1":
            success = build_app("simple")
        elif choice == "2":
            success = build_app("full")
        elif choice == "3":
            print("\n--- بناء النسخة المبسطة ---")
            success1 = build_app("simple")
            print("\n--- بناء النسخة الكاملة ---")
            success2 = build_app("full")
            success = success1 or success2
        else:
            print("اختيار غير صحيح، سيتم بناء النسخة المبسطة")
            success = build_app("simple")
        
        if success:
            print("\n🎉 تم الانتهاء من البناء!")
            print("\nملاحظات:")
            print("• تأكد من وضع ملفات الأذان في مجلد 'audio'")
            print("• يمكن تخصيص الإعدادات من خلال لوحة التحكم (مفتاح A)")
            print("• للحصول على أفضل أداء، شغل البرنامج على شاشة كبيرة")
        else:
            print("\n❌ فشل في البناء")
            print("تحقق من تثبيت جميع المتطلبات باستخدام:")
            print("install_requirements.bat")
    
    except KeyboardInterrupt:
        print("\n\nتم إلغاء العملية")
    except Exception as e:
        print(f"\nخطأ: {e}")

if __name__ == "__main__":
    main()
