.admin-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.admin-panel {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  border-radius: 20px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.admin-header {
  background: linear-gradient(135deg, #8B0000, #DC143C);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.admin-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.admin-tabs {
  display: flex;
  background: #2d2d2d;
  border-bottom: 2px solid #444;
}

.admin-tabs button {
  flex: 1;
  padding: 15px;
  background: none;
  border: none;
  color: #ccc;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 3px solid transparent;
}

.admin-tabs button:hover {
  background: #3d3d3d;
  color: white;
}

.admin-tabs button.active {
  background: #4d4d4d;
  color: white;
  border-bottom-color: #DC143C;
}

.admin-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
  color: white;
}

.admin-content h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #DC143C;
  font-size: 1.3rem;
}

.prayer-row {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.prayer-row label {
  display: block;
  font-weight: 600;
  margin-bottom: 10px;
  color: #DC143C;
}

.time-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 15px;
  align-items: center;
}

.time-inputs > div {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-inputs input[type="time"] {
  background: #1a1a1a;
  border: 2px solid #444;
  border-radius: 8px;
  padding: 8px;
  color: white;
  font-size: 1rem;
}

.time-inputs input[type="time"]:focus {
  border-color: #DC143C;
  outline: none;
}

.test-btn {
  background: linear-gradient(135deg, #228B22, #32CD32);
  border: none;
  border-radius: 8px;
  padding: 8px 15px;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  transition: transform 0.2s;
}

.test-btn:hover {
  transform: translateY(-2px);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.volume-control input[type="range"] {
  flex: 1;
  height: 6px;
  background: #444;
  border-radius: 3px;
  outline: none;
}

.volume-control input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  background: #DC143C;
  border-radius: 50%;
  cursor: pointer;
}

.audio-test {
  display: flex;
  gap: 15px;
}

.audio-test button {
  background: linear-gradient(135deg, #4169E1, #6495ED);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s;
}

.audio-test button:hover {
  transform: translateY(-2px);
}

.add-text {
  margin-bottom: 20px;
}

.add-text textarea {
  width: 100%;
  background: #1a1a1a;
  border: 2px solid #444;
  border-radius: 8px;
  padding: 10px;
  color: white;
  font-size: 1rem;
  resize: vertical;
  margin-bottom: 10px;
}

.add-text textarea:focus {
  border-color: #DC143C;
  outline: none;
}

.add-text button {
  background: linear-gradient(135deg, #DC143C, #B22222);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s;
}

.add-text button:hover {
  transform: translateY(-2px);
}

.texts-list {
  max-height: 200px;
  overflow-y: auto;
}

.text-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.text-item button {
  background: #DC143C;
  border: none;
  border-radius: 5px;
  padding: 5px 10px;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
}

.setting-row {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.setting-row input[type="text"] {
  flex: 1;
  background: #1a1a1a;
  border: 2px solid #444;
  border-radius: 8px;
  padding: 8px;
  color: white;
  font-size: 1rem;
}

.setting-row input[type="text"]:focus {
  border-color: #DC143C;
  outline: none;
}

.admin-footer {
  padding: 20px;
  background: #2d2d2d;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  border-top: 2px solid #444;
}

.save-btn {
  background: linear-gradient(135deg, #228B22, #32CD32);
  border: none;
  border-radius: 8px;
  padding: 12px 25px;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: transform 0.2s;
}

.save-btn:hover {
  transform: translateY(-2px);
}

.cancel-btn {
  background: linear-gradient(135deg, #666, #888);
  border: none;
  border-radius: 8px;
  padding: 12px 25px;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  transition: transform 0.2s;
}

.cancel-btn:hover {
  transform: translateY(-2px);
}
