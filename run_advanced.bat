@echo off
chcp 65001 >nul
title شاشة أوقات الصلاة - النسخة المتقدمة

echo.
echo ================================================================
echo 🕌 شاشة أوقات الصلاة للمسجد - النسخة المتقدمة
echo ================================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

echo.
echo 🔍 فحص المتطلبات...

REM التحقق من المكتبات الأساسية
python -c "import tkinter, requests, pygame, PIL, hijri_converter, pyttsx3" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ بعض المكتبات الأساسية مفقودة
    echo.
    echo هل تريد تثبيت المتطلبات الأساسية؟ (y/n)
    set /p install_basic=
    if /i "%install_basic%"=="y" (
        echo.
        echo 📦 تثبيت المتطلبات الأساسية...
        python -m pip install -r requirements.txt
        if errorlevel 1 (
            echo ❌ فشل في تثبيت المتطلبات الأساسية
            pause
            exit /b 1
        )
        echo ✅ تم تثبيت المتطلبات الأساسية
    ) else (
        echo ⚠️ سيتم تشغيل النسخة الأساسية
        goto :run_basic
    )
)

REM التحقق من المكتبات المتقدمة
python -c "import matplotlib, pandas, numpy, cv2, flask, sqlalchemy" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ بعض المكتبات المتقدمة مفقودة
    echo.
    echo هل تريد تثبيت المتطلبات المتقدمة؟ (y/n)
    set /p install_advanced=
    if /i "%install_advanced%"=="y" (
        echo.
        echo 📦 تثبيت المتطلبات المتقدمة...
        echo هذا قد يستغرق عدة دقائق...
        python -m pip install -r requirements_advanced.txt
        if errorlevel 1 (
            echo ⚠️ فشل في تثبيت بعض المتطلبات المتقدمة
            echo سيتم تشغيل النسخة الأساسية
            goto :run_basic
        )
        echo ✅ تم تثبيت المتطلبات المتقدمة
    ) else (
        echo ⚠️ سيتم تشغيل النسخة الأساسية
        goto :run_basic
    )
)

echo.
echo 🚀 تشغيل النسخة المتقدمة...
echo.
echo الميزات المتاحة:
echo ✅ نظام الإشعارات المتقدم
echo ✅ إدارة المحتوى الديناميكي
echo ✅ التقويم الإسلامي المتقدم
echo ✅ الإحصائيات والتقارير
echo ✅ التحكم عن بُعد (http://localhost:8080)
echo.

REM تشغيل النسخة المتقدمة
python run_advanced.py
goto :end

:run_basic
echo.
echo 🚀 تشغيل النسخة الأساسية...
echo.
python main.py
goto :end

:end
echo.
echo 👋 شكراً لاستخدام شاشة أوقات الصلاة
echo.
pause
