# شاشة أوقات الصلاة للمسجد
## Mosque Prayer Times Display

برنامج مخصص لعرض توقيت الصلاة على شاشة المسجد مع تصميم أنيق باللغة العربية ودعم كامل للمحتوى الإسلامي.

## ✨ المميزات الرئيسية

### 🕌 الواجهة الرئيسية
- **تصميم أنيق**: خلفية حمراء مزخرفة تحاكي التصميم التقليدي للمساجد
- **عرض الوقت**: ساعة رقمية كبيرة في المنتصف مع تحديث مباشر
- **التاريخ المزدوج**: عرض التاريخ الهجري والميلادي
- **معلومات الطقس**: درجة الحرارة ورمز الطقس الحالي
- **علم الدولة**: يظهر تلقائياً حسب الموقع الجغرافي

### 🕐 أوقات الصلاة
- **ستة أوقات**: الفجر، الشروق، الظهر، العصر، المغرب، العشاء
- **توقيت مزدوج**: وقت الأذان ووقت الإقامة لكل صلاة
- **حساب دقيق**: استخدام API متخصص لحساب الأوقات حسب الموقع
- **عد تنازلي**: دائرة تعرض الوقت المتبقي للصلاة القادمة

### 🔊 نظام الأذان
- **تشغيل تلقائي**: الأذان يُشغل تلقائياً في الوقت المحدد
- **تحكم في الصوت**: إمكانية تعديل مستوى الصوت
- **أصوات متنوعة**: دعم ملفات صوتية مختلفة للأذان

### 📖 النصوص الإسلامية
- **عرض متناوب**: آيات قرآنية وأحاديث نبوية تتغير كل 10 ثوانٍ
- **خط جميل**: استخدام خط عربي أنيق للنصوص
- **إدارة المحتوى**: إمكانية إضافة وحذف النصوص

### ⚙️ لوحة التحكم الإدارية
- **تعديل الأوقات**: تخصيص أوقات الأذان والإقامة
- **إعدادات الصوت**: تحكم في مستوى الصوت واختبار الأذان
- **إدارة النصوص**: إضافة وحذف الآيات والأحاديث
- **إعدادات عامة**: تخصيص اسم المسجد والإعدادات الأخرى

## 🚀 التشغيل والتثبيت

### متطلبات النظام
- Node.js (الإصدار 16 أو أحدث)
- متصفح ويب حديث
- اتصال بالإنترنت (لجلب أوقات الصلاة والطقس)

### خطوات التثبيت

1. **تحميل المشروع**
```bash
git clone [repository-url]
cd mosque-prayer-display
```

2. **تثبيت المكتبات**
```bash
npm install
```

3. **تشغيل المشروع**
```bash
npm run dev
```

4. **فتح المتصفح**
انتقل إلى `http://localhost:3000`

### البناء للإنتاج
```bash
npm run build
```

## 🎮 كيفية الاستخدام

### الواجهة الرئيسية
- الشاشة تعمل تلقائياً وتعرض جميع المعلومات
- الوقت والتاريخ يتحدثان تلقائياً
- النصوص الإسلامية تتغير كل 10 ثوانٍ

### فتح لوحة التحكم
- اضغط على مفتاح `A` لفتح لوحة التحكم الإدارية
- اضغط على `Escape` لإغلاق لوحة التحكم

### تعديل الإعدادات
1. **أوقات الصلاة**: قم بتعديل أوقات الأذان والإقامة
2. **إعدادات الصوت**: اضبط مستوى الصوت واختبر الأذان
3. **النصوص الإسلامية**: أضف أو احذف الآيات والأحاديث
4. **الإعدادات العامة**: غيّر اسم المسجد

## 🛠️ التقنيات المستخدمة

- **React 18**: مكتبة واجهة المستخدم
- **Vite**: أداة البناء والتطوير
- **CSS3**: التصميم والتنسيق مع دعم RTL
- **AlAdhan API**: حساب أوقات الصلاة
- **Geolocation API**: تحديد الموقع الجغرافي
- **Web Audio API**: تشغيل الأذان
- **LocalStorage**: حفظ الإعدادات محلياً

## 📁 هيكل المشروع

```
src/
├── components/          # مكونات الواجهة
│   ├── Header.jsx      # رأس الصفحة
│   ├── MainContent.jsx # المحتوى الرئيسي
│   ├── TimeDisplay.jsx # عرض الوقت
│   ├── PrayerTimes.jsx # أوقات الصلاة
│   ├── CountdownCircle.jsx # العد التنازلي
│   ├── Footer.jsx      # النصوص الإسلامية
│   └── AdminPanel.jsx  # لوحة التحكم
├── services/           # الخدمات
│   ├── prayerTimesService.js # حساب أوقات الصلاة
│   ├── weatherService.js     # بيانات الطقس
│   └── azanService.js        # نظام الأذان
├── utils/              # الأدوات المساعدة
│   └── dateUtils.js    # تنسيق التواريخ
├── App.jsx             # المكون الرئيسي
├── main.jsx           # نقطة الدخول
└── index.css          # التصميم الرئيسي
```

## 🔧 التخصيص والتطوير

### إضافة أصوات أذان جديدة
1. ضع ملفات الصوت في مجلد `public/audio/`
2. حدّث مسارات الملفات في `azanService.js`

### تخصيص التصميم
- عدّل ألوان الخلفية في `index.css`
- غيّر الخطوط من خلال تحديث روابط Google Fonts
- اضبط أحجام العناصر والمسافات

### إضافة لغات جديدة
- أضف ملفات الترجمة
- حدّث مكونات النصوص لدعم اللغات المتعددة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**الأذان لا يعمل تلقائياً:**
- تأكد من السماح للمتصفح بتشغيل الصوت
- انقر في أي مكان على الصفحة أولاً لتفعيل الصوت

**أوقات الصلاة غير دقيقة:**
- تحقق من إعدادات الموقع الجغرافي
- تأكد من اتصال الإنترنت لجلب البيانات

**التصميم لا يظهر بشكل صحيح:**
- تأكد من دعم المتصفح لـ CSS Grid و Flexbox
- جرب تحديث المتصفح

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راجع الوثائق التقنية
- تحقق من console المتصفح للأخطاء

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الحر في المساجد والمؤسسات الإسلامية.

---

**تم تطوير هذا البرنامج بعناية لخدمة المجتمع الإسلامي وتسهيل أداء الصلوات في أوقاتها المحددة.**
