#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكون رأس الصفحة - عرض الطقس والتاريخ واسم المسجد
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
import arabic_reshaper
from bidi.algorithm import get_display

class HeaderFrame(tk.Frame):
    """إطار رأس الصفحة"""
    
    def __init__(self, parent, app):
        super().__init__(parent, bg='#8B0000')
        self.app = app
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة رأس الصفحة"""
        # تقسيم الرأس إلى ثلاثة أقسام
        self.grid_columnconfigure(0, weight=1)
        self.grid_columnconfigure(1, weight=1)
        self.grid_columnconfigure(2, weight=1)
        
        # القسم الأيسر - معلومات الطقس
        self.weather_frame = tk.Frame(self, bg='#8B0000')
        self.weather_frame.grid(row=0, column=0, sticky='w', padx=20)
        
        # علم الدولة
        self.flag_label = tk.Label(
            self.weather_frame,
            text="🇩🇿",
            font=self.app.get_font('large'),
            bg='#8B0000',
            fg='white'
        )
        self.flag_label.pack(side=tk.LEFT, padx=(0, 15))
        
        # معلومات الطقس
        self.weather_info_frame = tk.Frame(self.weather_frame, bg='#8B0000')
        self.weather_info_frame.pack(side=tk.LEFT)
        
        self.temp_label = tk.Label(
            self.weather_info_frame,
            text="☀️ 30°",
            font=self.app.get_font('medium'),
            bg='#8B0000',
            fg='white'
        )
        self.temp_label.pack()
        
        self.humidity_label = tk.Label(
            self.weather_info_frame,
            text="41° 28°",
            font=self.app.get_font('small'),
            bg='#8B0000',
            fg='white'
        )
        self.humidity_label.pack()
        
        # القسم الأوسط - التاريخ
        self.date_frame = tk.Frame(self, bg='#8B0000')
        self.date_frame.grid(row=0, column=1)
        
        self.gregorian_date_label = tk.Label(
            self.date_frame,
            text="",
            font=self.app.get_font('medium'),
            bg='#8B0000',
            fg='white'
        )
        self.gregorian_date_label.pack()
        
        self.hijri_date_label = tk.Label(
            self.date_frame,
            text="",
            font=self.app.get_font('medium'),
            bg='#8B0000',
            fg='white'
        )
        self.hijri_date_label.pack()
        
        # القسم الأيمن - اسم المسجد
        self.mosque_frame = tk.Frame(self, bg='#8B0000')
        self.mosque_frame.grid(row=0, column=2, sticky='e', padx=20)
        
        self.mosque_label = tk.Label(
            self.mosque_frame,
            text=self.format_arabic_text("اسم المسجد"),
            font=self.app.get_font('title'),
            bg='#8B0000',
            fg='white'
        )
        self.mosque_label.pack()
        
        # تحديث التاريخ الأولي
        self.update_dates()
    
    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
    
    def update_dates(self):
        """تحديث التواريخ"""
        now = datetime.now()
        
        # التاريخ الميلادي
        gregorian_date = now.strftime("%A %d %B %Y")
        # ترجمة أسماء الأيام والشهور للعربية
        day_names = {
            'Monday': 'الاثنين',
            'Tuesday': 'الثلاثاء', 
            'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس',
            'Friday': 'الجمعة',
            'Saturday': 'السبت',
            'Sunday': 'الأحد'
        }
        
        month_names = {
            'January': 'يناير',
            'February': 'فبراير',
            'March': 'مارس',
            'April': 'أبريل',
            'May': 'مايو',
            'June': 'يونيو',
            'July': 'يوليو',
            'August': 'أغسطس',
            'September': 'سبتمبر',
            'October': 'أكتوبر',
            'November': 'نوفمبر',
            'December': 'ديسمبر'
        }
        
        # استبدال الأسماء الإنجليزية بالعربية
        for eng, ar in day_names.items():
            gregorian_date = gregorian_date.replace(eng, ar)
        for eng, ar in month_names.items():
            gregorian_date = gregorian_date.replace(eng, ar)
        
        self.gregorian_date_label.config(text=self.format_arabic_text(gregorian_date))
        
        # التاريخ الهجري (تقريبي)
        hijri_date = "5 ذو الحجة 1446 هـ"
        self.hijri_date_label.config(text=self.format_arabic_text(hijri_date))
    
    def update_weather(self, weather_data):
        """تحديث معلومات الطقس"""
        if weather_data:
            temp = weather_data.get('temperature', 30)
            humidity = weather_data.get('humidity', 41)
            feels_like = weather_data.get('feels_like', 28)
            condition = weather_data.get('condition', 'clear')
            
            # رمز الطقس
            weather_icon = self.get_weather_icon(condition)
            
            self.temp_label.config(text=f"{weather_icon} {temp}°")
            self.humidity_label.config(text=f"{humidity}° {feels_like}°")
            
            # تحديث علم الدولة حسب الموقع
            country = weather_data.get('country', 'DZ')
            flag = self.get_country_flag(country)
            self.flag_label.config(text=flag)
    
    def get_weather_icon(self, condition):
        """الحصول على رمز الطقس"""
        icons = {
            'clear': '☀️',
            'sunny': '☀️',
            'cloudy': '☁️',
            'partly_cloudy': '⛅',
            'rain': '🌧️',
            'storm': '⛈️',
            'snow': '❄️',
            'fog': '🌫️'
        }
        return icons.get(condition.lower(), '☀️')
    
    def get_country_flag(self, country_code):
        """الحصول على علم الدولة"""
        flags = {
            'DZ': '🇩🇿',  # الجزائر
            'SA': '🇸🇦',  # السعودية
            'EG': '🇪🇬',  # مصر
            'MA': '🇲🇦',  # المغرب
            'TN': '🇹🇳',  # تونس
            'AE': '🇦🇪',  # الإمارات
            'QA': '🇶🇦',  # قطر
            'KW': '🇰🇼',  # الكويت
            'BH': '🇧🇭',  # البحرين
            'OM': '🇴🇲',  # عمان
            'JO': '🇯🇴',  # الأردن
            'LB': '🇱🇧',  # لبنان
            'SY': '🇸🇾',  # سوريا
            'IQ': '🇮🇶',  # العراق
            'YE': '🇾🇪',  # اليمن
            'LY': '🇱🇾',  # ليبيا
            'SD': '🇸🇩',  # السودان
        }
        return flags.get(country_code.upper(), '🌍')
    
    def update_mosque_name(self, name):
        """تحديث اسم المسجد"""
        self.mosque_label.config(text=self.format_arabic_text(name))
