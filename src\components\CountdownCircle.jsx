import React, { useState, useEffect } from 'react';

const CountdownCircle = ({ currentTime, prayerTimes, position = 'left' }) => {
  const [nextPrayer, setNextPrayer] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState('');

  const defaultTimes = {
    fajr: '04:11',
    dhuhr: '12:20',
    asr: '15:37',
    maghrib: '19:01',
    isha: '20:31'
  };

  const prayerNames = {
    fajr: 'الفجر',
    dhuhr: 'الظهر',
    asr: 'العصر',
    maghrib: 'المغرب',
    isha: 'العشاء'
  };

  useEffect(() => {
    const findNextPrayer = () => {
      const now = currentTime;
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      const currentTimeInMinutes = currentHour * 60 + currentMinute;

      const times = prayerTimes || defaultTimes;
      const prayerList = Object.entries(times).map(([key, time]) => {
        const timeStr = typeof time === 'object' ? time.azan : time;
        const [hour, minute] = timeStr.split(':').map(Number);
        return {
          name: key,
          displayName: prayerNames[key],
          timeInMinutes: hour * 60 + minute,
          timeStr: timeStr
        };
      });

      // Sort prayers by time
      prayerList.sort((a, b) => a.timeInMinutes - b.timeInMinutes);

      // Find next prayer
      let next = prayerList.find(prayer => prayer.timeInMinutes > currentTimeInMinutes);
      
      // If no prayer found for today, take first prayer of next day
      if (!next) {
        next = prayerList[0];
        next.isNextDay = true;
      }

      setNextPrayer(next);
    };

    findNextPrayer();
  }, [currentTime, prayerTimes]);

  useEffect(() => {
    const calculateTimeRemaining = () => {
      if (!nextPrayer) return;

      const now = currentTime;
      const [hour, minute] = nextPrayer.timeStr.split(':').map(Number);
      
      let targetTime = new Date(now);
      targetTime.setHours(hour, minute, 0, 0);
      
      // If it's next day
      if (nextPrayer.isNextDay || targetTime <= now) {
        targetTime.setDate(targetTime.getDate() + 1);
      }

      const diff = targetTime - now;
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      if (hours > 0) {
        setTimeRemaining(`${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
      } else {
        setTimeRemaining(`${minutes}:${seconds.toString().padStart(2, '0')}`);
      }
    };

    calculateTimeRemaining();
  }, [currentTime, nextPrayer]);

  if (position === 'right') {
    return (
      <div className="countdown-circle">
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '1rem', marginBottom: '10px' }}>
            {nextPrayer?.displayName || 'الفجر'}
          </div>
          <div style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>
            {timeRemaining || '1:58'}
          </div>
        </div>
      </div>
    );
  }

  // Left side - mosque logo
  return (
    <div className="countdown-circle">
      <div style={{ textAlign: 'center' }}>
        <div style={{ fontSize: '3rem', marginBottom: '10px' }}>
          🕌
        </div>
        <div style={{ fontSize: '1rem' }}>
          المسجد
        </div>
      </div>
    </div>
  );
};

export default CountdownCircle;
